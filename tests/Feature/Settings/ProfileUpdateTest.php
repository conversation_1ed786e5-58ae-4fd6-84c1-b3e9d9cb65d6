<?php

use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('profile page is displayed', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->get('/settings/profile');

    $response->assertOk();
});

test('profile information can be updated', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+*************',
            'bank_name' => 'Bank BCA',
            'account_number' => '**********',
            'account_holder_name' => 'Test User',
            'withdrawal_limit' => '5000000',
        ]);

    $response
        ->assertSessionHasNoErrors()
        ->assertRedirect('/settings/profile');

    $user->refresh();

    expect($user->name)->toBe('Test User');
    expect($user->email)->toBe('<EMAIL>');
    expect($user->phone)->toBe('+*************');
    expect($user->bank_name)->toBe('Bank BCA');
    expect($user->account_number)->toBe('**********');
    expect($user->account_holder_name)->toBe('Test User');
    expect($user->withdrawal_limit)->toBe(5000000.00);
    expect($user->email_verified_at)->toBeNull();
});

test('phone number is required for profile update', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'bank_name' => '',
            'account_number' => '',
            'account_holder_name' => '',
            'withdrawal_limit' => '',
        ]);

    $response->assertSessionHasErrors('phone');
});

test('phone number must start with +62 for profile update', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '08**********',
            'bank_name' => '',
            'account_number' => '',
            'account_holder_name' => '',
            'withdrawal_limit' => '',
        ]);

    $response->assertSessionHasErrors('phone');
});

test('phone number must be unique for profile update', function () {
    $user1 = User::factory()->create(['phone' => '+*************']);
    $user2 = User::factory()->create(['phone' => '+*************']);

    $response = $this
        ->actingAs($user2)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+*************', // Same as user1
            'bank_name' => '',
            'account_number' => '',
            'account_holder_name' => '',
            'withdrawal_limit' => '',
        ]);

    $response->assertSessionHasErrors('phone');
});

test('user can keep their own phone number when updating profile', function () {
    $user = User::factory()->create(['phone' => '+*************']);

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Updated Name',
            'email' => $user->email,
            'phone' => '+*************', // Same phone number
            'bank_name' => '',
            'account_number' => '',
            'account_holder_name' => '',
            'withdrawal_limit' => '',
        ]);

    $response
        ->assertSessionHasNoErrors()
        ->assertRedirect('/settings/profile');

    $user->refresh();
    expect($user->name)->toBe('Updated Name');
    expect($user->phone)->toBe('+*************');
});

test('bank account details are optional', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+*************',
            'bank_name' => '',
            'account_number' => '',
            'account_holder_name' => '',
            'withdrawal_limit' => '',
        ]);

    $response
        ->assertSessionHasNoErrors()
        ->assertRedirect('/settings/profile');

    $user->refresh();
    expect($user->bank_name)->toBeNull();
    expect($user->account_number)->toBeNull();
    expect($user->account_holder_name)->toBeNull();
    expect($user->withdrawal_limit)->toBeNull();
});

test('withdrawal limit must be numeric', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+*************',
            'bank_name' => 'Bank BCA',
            'account_number' => '**********',
            'account_holder_name' => 'Test User',
            'withdrawal_limit' => 'invalid_amount',
        ]);

    $response->assertSessionHasErrors('withdrawal_limit');
});

test('withdrawal limit cannot be negative', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+*************',
            'bank_name' => 'Bank BCA',
            'account_number' => '**********',
            'account_holder_name' => 'Test User',
            'withdrawal_limit' => '-1000',
        ]);

    $response->assertSessionHasErrors('withdrawal_limit');
});

test('email verification status is unchanged when the email address is unchanged', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => $user->email,
            'phone' => $user->phone,
            'bank_name' => '',
            'account_number' => '',
            'account_holder_name' => '',
            'withdrawal_limit' => '',
        ]);

    $response
        ->assertSessionHasNoErrors()
        ->assertRedirect('/settings/profile');

    expect($user->refresh()->email_verified_at)->not->toBeNull();
});

test('user can delete their account', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->delete('/settings/profile', [
            'password' => 'password',
        ]);

    $response
        ->assertSessionHasNoErrors()
        ->assertRedirect('/');

    $this->assertGuest();
    expect($user->fresh())->toBeNull();
});

test('correct password must be provided to delete account', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->from('/settings/profile')
        ->delete('/settings/profile', [
            'password' => 'wrong-password',
        ]);

    $response
        ->assertSessionHasErrors('password')
        ->assertRedirect('/settings/profile');

    expect($user->fresh())->not->toBeNull();
});