<?php

use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('profile page is displayed', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->get('/settings/profile');

    $response->assertOk();
});

test('profile information can be updated', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+6281234567890',
        ]);

    $response
        ->assertSessionHasNoErrors()
        ->assertRedirect('/settings/profile');

    $user->refresh();

    expect($user->name)->toBe('Test User');
    expect($user->email)->toBe('<EMAIL>');
    expect($user->phone)->toBe('+6281234567890');
    expect($user->email_verified_at)->toBeNull();
});

test('phone number is required for profile update', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

    $response->assertSessionHasErrors('phone');
});

test('phone number must start with +62 for profile update', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '081234567890',
        ]);

    $response->assertSessionHasErrors('phone');
});

test('phone number must be unique for profile update', function () {
    $user1 = User::factory()->create(['phone' => '+6281234567890']);
    $user2 = User::factory()->create(['phone' => '+6281234567891']);

    $response = $this
        ->actingAs($user2)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+6281234567890', // Same as user1
        ]);

    $response->assertSessionHasErrors('phone');
});

test('user can keep their own phone number when updating profile', function () {
    $user = User::factory()->create(['phone' => '+6281234567890']);

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Updated Name',
            'email' => $user->email,
            'phone' => '+6281234567890', // Same phone number
        ]);

    $response
        ->assertSessionHasNoErrors()
        ->assertRedirect('/settings/profile');

    $user->refresh();
    expect($user->name)->toBe('Updated Name');
    expect($user->phone)->toBe('+6281234567890');
});

test('email verification status is unchanged when the email address is unchanged', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => $user->email,
            'phone' => $user->phone,
        ]);

    $response
        ->assertSessionHasNoErrors()
        ->assertRedirect('/settings/profile');

    expect($user->refresh()->email_verified_at)->not->toBeNull();
});

test('user can delete their account', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->delete('/settings/profile', [
            'password' => 'password',
        ]);

    $response
        ->assertSessionHasNoErrors()
        ->assertRedirect('/');

    $this->assertGuest();
    expect($user->fresh())->toBeNull();
});

test('correct password must be provided to delete account', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->from('/settings/profile')
        ->delete('/settings/profile', [
            'password' => 'wrong-password',
        ]);

    $response
        ->assertSessionHasErrors('password')
        ->assertRedirect('/settings/profile');

    expect($user->fresh())->not->toBeNull();
});