<?php

use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('admin can access user management page', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $users = User::factory()->count(3)->create(['role' => 'user']);

    $response = $this->actingAs($admin)->get('/menuadmin');

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => 
        $page->component('menuadmin')
            ->has('users.data', 3)
    );
});

test('regular user cannot access user management page', function () {
    $user = User::factory()->create(['role' => 'user']);

    $response = $this->actingAs($user)->get('/menuadmin');

    $response->assertStatus(403);
});

test('admin can access edit user page', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $user = User::factory()->create(['role' => 'user']);

    $response = $this->actingAs($admin)->get("/admin/users/{$user->id}/edit");

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => 
        $page->component('admin/edit-user')
            ->where('user.id', $user->id)
            ->where('user.name', $user->name)
    );
});

test('admin cannot edit admin users', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $anotherAdmin = User::factory()->create(['role' => 'admin']);

    $response = $this->actingAs($admin)->get("/admin/users/{$anotherAdmin->id}/edit");

    $response->assertStatus(404);
});

test('admin can update user information', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $user = User::factory()->create(['role' => 'user']);

    $updateData = [
        'name' => 'Updated Name',
        'email' => '<EMAIL>',
        'phone' => '+*************',
        'bank_name' => 'Bank BCA',
        'account_number' => '**********',
        'account_holder_name' => 'Updated Name',
        'withdrawal_limit' => '5000000',
    ];

    $response = $this->actingAs($admin)->patch("/admin/users/{$user->id}", $updateData);

    $response->assertRedirect("/admin/users/{$user->id}/edit");
    
    $user->refresh();
    expect($user->name)->toBe('Updated Name');
    expect($user->email)->toBe('<EMAIL>');
    expect($user->phone)->toBe('+*************');
    expect($user->bank_name)->toBe('Bank BCA');
});

test('regular user cannot update user information', function () {
    $user = User::factory()->create(['role' => 'user']);
    $targetUser = User::factory()->create(['role' => 'user']);

    $updateData = [
        'name' => 'Updated Name',
        'email' => '<EMAIL>',
        'phone' => '+*************',
    ];

    $response = $this->actingAs($user)->patch("/admin/users/{$targetUser->id}", $updateData);

    $response->assertStatus(403);
});

test('admin cannot update user with invalid data', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $user = User::factory()->create(['role' => 'user']);

    $updateData = [
        'name' => '', // Required field
        'email' => 'invalid-email', // Invalid email
        'phone' => '123', // Invalid phone format
    ];

    $response = $this->actingAs($admin)->patch("/admin/users/{$user->id}", $updateData);

    $response->assertSessionHasErrors(['name', 'email', 'phone']);
});

test('user management page shows only users with role user', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $users = User::factory()->count(3)->create(['role' => 'user']);
    $admins = User::factory()->count(2)->create(['role' => 'admin']);

    $response = $this->actingAs($admin)->get('/menuadmin');

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => 
        $page->component('menuadmin')
            ->has('users.data', 3) // Only 3 users, not including admins
    );
});
