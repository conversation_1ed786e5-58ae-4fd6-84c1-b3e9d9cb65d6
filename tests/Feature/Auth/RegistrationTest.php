<?php

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('registration screen can be rendered', function () {
    $response = $this->get('/register');

    $response->assertStatus(200);
});

test('new users can register', function () {
    $response = $this->post('/register', [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'phone' => '+6281234567890',
        'password' => 'password',
        'password_confirmation' => 'password',
    ]);

    $this->assertAuthenticated();
    $response->assertRedirect(route('dashboard', absolute: false));
});

test('phone number is required', function () {
    $response = $this->post('/register', [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => 'password',
        'password_confirmation' => 'password',
    ]);

    $response->assertSessionHasErrors('phone');
});

test('phone number must start with +62', function () {
    $response = $this->post('/register', [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'phone' => '081234567890',
        'password' => 'password',
        'password_confirmation' => 'password',
    ]);

    $response->assertSessionHasErrors('phone');
});

test('phone number must be unique', function () {
    // Create first user
    \App\Models\User::factory()->create([
        'phone' => '+6281234567890'
    ]);

    // Try to register with same phone
    $response = $this->post('/register', [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'phone' => '+6281234567890',
        'password' => 'password',
        'password_confirmation' => 'password',
    ]);

    $response->assertSessionHasErrors('phone');
});