<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Middleware\IsAdmin;
use App\Http\Middleware\IsUser;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', IsUser::class])->group(function () {
    Route::get('/dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
});

// Halaman untuk meminta link reset password
Route::get('forgot-password', [\App\Http\Controllers\Auth\PasswordResetController::class, 'showLinkRequestForm'])
     ->name('password.request');

// Rute untuk mengirim link reset password ke email
Route::post('forgot-password', [\App\Http\Controllers\Auth\PasswordResetController::class, 'sendResetLinkEmail'])
     ->name('password.email');

// Halaman untuk reset password
Route::get('reset-password/{token}', [\App\Http\Controllers\Auth\PasswordResetController::class, 'showResetForm'])
     ->name('password.reset');

// Proses untuk mengubah password
Route::post('reset-password', [\App\Http\Controllers\Auth\PasswordResetController::class, 'reset'])->name('password.update');

Route::middleware(['auth', IsAdmin::class])->group(function () {
    Route::get('/DashboardAdmin', function () {
        return Inertia::render('DashboardAdmin');
    })->name('DashboardAdmin');
});

Route::middleware(['auth', IsAdmin::class])->group(function () {
    Route::get('/test-shadcn', function () {
        return Inertia::render('TestShadcn');
    })->name('test.shadcn');
});

Route::middleware(['auth', IsAdmin::class])->group(function () {
    // Menu Admin - User Management
    Route::get('/menuadmin', [App\Http\Controllers\MenuAdminController::class, 'index'])->name('admin.users.index');

    // User Edit Routes
    Route::get('/admin/users/{user}/edit', [App\Http\Controllers\UserEditController::class, 'edit'])->name('admin.users.edit');
    Route::patch('/admin/users/{user}', [App\Http\Controllers\UserEditController::class, 'update'])->name('admin.users.update');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
