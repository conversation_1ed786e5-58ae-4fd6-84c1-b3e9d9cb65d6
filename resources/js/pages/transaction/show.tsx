import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, XCircle, Clock } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';

interface Transaction {
    id: number;
    reference_no: string;
    merchant_reference: string | null;
    amount: number;
    fee: number;
    status: 'pending' | 'success' | 'failed';
    customer_name: string;
    customer_email: string | null;
    customer_phone: string | null;
    created_at: string;
    updated_at: string;
    merchant: {
        id: number;
        merchant_name: string;
        merchant_code: string;
    };
}

interface TransactionShowProps {
    transaction: Transaction;
}

const breadcrumbItems: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Daftar Transaksi', href: '/transaction' },
    { title: 'Detail Transaksi', href: '#' },
];

export default function TransactionShow({ transaction }: TransactionShowProps) {
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
        });
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'success':
                return (
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Berhasil
                    </Badge>
                );
            case 'pending':
                return (
                    <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
                        <Clock className="h-3 w-3 mr-1" />
                        Pending
                    </Badge>
                );
            case 'failed':
                return (
                    <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
                        <XCircle className="h-3 w-3 mr-1" />
                        Gagal
                    </Badge>
                );
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbItems}>
            <Head title={`Detail Transaksi - ${transaction.reference_no}`} />
            
            <div className="space-y-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="space-y-1">
                        <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm" asChild>
                                <Link href={route('transaction.index')}>
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Kembali
                                </Link>
                            </Button>
                        </div>
                        <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">Detail Transaksi</h1>
                        <p className="text-sm text-muted-foreground sm:text-base">
                            Informasi lengkap transaksi {transaction.reference_no}
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        {getStatusBadge(transaction.status)}
                    </div>
                </div>

                {/* Transaction Details */}
                <div className="grid gap-6 md:grid-cols-2">
                    {/* Transaction Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Informasi Transaksi</CardTitle>
                            <CardDescription>Detail informasi transaksi</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-3 gap-4">
                                <div className="space-y-1">
                                    <p className="text-sm font-medium text-muted-foreground">Referensi</p>
                                    <p className="text-sm font-mono">{transaction.reference_no}</p>
                                </div>
                                <div className="space-y-1">
                                    <p className="text-sm font-medium text-muted-foreground">Merchant Ref</p>
                                    <p className="text-sm font-mono">{transaction.merchant_reference || '-'}</p>
                                </div>
                                <div className="space-y-1">
                                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                                    {getStatusBadge(transaction.status)}
                                </div>
                            </div>
                            
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-1">
                                    <p className="text-sm font-medium text-muted-foreground">Jumlah</p>
                                    <p className="text-lg font-bold">{formatCurrency(transaction.amount)}</p>
                                </div>
                                <div className="space-y-1">
                                    <p className="text-sm font-medium text-muted-foreground">Fee</p>
                                    <p className="text-lg font-bold">{formatCurrency(transaction.fee)}</p>
                                </div>
                            </div>

                            <div className="space-y-1">
                                <p className="text-sm font-medium text-muted-foreground">Jumlah Bersih</p>
                                <p className="text-xl font-bold text-green-600">
                                    {formatCurrency(transaction.amount - transaction.fee)}
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Customer Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Informasi Customer</CardTitle>
                            <CardDescription>Detail informasi customer</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-muted-foreground">Nama</p>
                                <p className="text-sm">{transaction.customer_name}</p>
                            </div>
                            
                            {transaction.customer_email && (
                                <div className="space-y-1">
                                    <p className="text-sm font-medium text-muted-foreground">Email</p>
                                    <p className="text-sm">{transaction.customer_email}</p>
                                </div>
                            )}
                            
                            {transaction.customer_phone && (
                                <div className="space-y-1">
                                    <p className="text-sm font-medium text-muted-foreground">Telepon</p>
                                    <p className="text-sm">{transaction.customer_phone}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Merchant Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Informasi Merchant</CardTitle>
                            <CardDescription>Detail informasi merchant</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-muted-foreground">Nama Merchant</p>
                                <p className="text-sm">{transaction.merchant.merchant_name}</p>
                            </div>
                            
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-muted-foreground">Kode Merchant</p>
                                <p className="text-sm font-mono">{transaction.merchant.merchant_code}</p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Timeline */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Timeline</CardTitle>
                            <CardDescription>Riwayat waktu transaksi</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-muted-foreground">Dibuat</p>
                                <p className="text-sm">{formatDate(transaction.created_at)}</p>
                            </div>
                            
                            <div className="space-y-1">
                                <p className="text-sm font-medium text-muted-foreground">Diperbarui</p>
                                <p className="text-sm">{formatDate(transaction.updated_at)}</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
