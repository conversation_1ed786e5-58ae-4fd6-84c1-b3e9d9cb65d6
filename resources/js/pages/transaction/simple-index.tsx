import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { Plus, TrendingUp } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';

interface Transaction {
    id: number;
    transaction_reference: string;
    amount: number;
    fee: number;
    status: 'pending' | 'success' | 'failed';
    payment_method: string;
    customer_name: string;
    customer_email: string | null;
    customer_phone: string | null;
    description: string | null;
    created_at: string;
    merchant: {
        id: number;
        merchant_name: string;
        merchant_code: string;
    };
}

interface TransactionIndexProps {
    transactions: {
        data: Transaction[];
        total: number;
    };
    stats: {
        total_transactions: number;
        successful_transactions: number;
        pending_transactions: number;
        failed_transactions: number;
        total_amount: number;
        total_fee: number;
    };
}

const breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Daftar Transaksi', href: '/transaction' },
];

export default function TransactionIndex({ transactions, stats }: TransactionIndexProps) {
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getStatusBadge = (status: string) => {
        const statusClasses = {
            success: 'bg-green-100 text-green-800 border-green-200',
            pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
            failed: 'bg-red-100 text-red-800 border-red-200',
        };

        const statusClass = statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800 border-gray-200';

        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusClass}`}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
        );
    };

    const handleGenerateSampleData = () => {
        router.post(route('sample.generate'));
    };

    return (
        <AppLayout breadcrumbItems={breadcrumbItems}>
            <Head title="Daftar Transaksi" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="space-y-1">
                        <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">Daftar Transaksi</h1>
                        <p className="text-sm text-muted-foreground sm:text-base">
                            Monitor all transactions across your merchants
                        </p>
                    </div>
                    {transactions.total === 0 && (
                        <Button 
                            onClick={handleGenerateSampleData}
                            className="w-full sm:w-auto"
                        >
                            <Plus className="h-4 w-4 mr-2" />
                            Generate Sample Data
                        </Button>
                    )}
                </div>

                {/* Statistics Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_transactions}</div>
                            <p className="text-xs text-muted-foreground">All time transactions</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Successful</CardTitle>
                            <div className="h-4 w-4 rounded-full bg-green-500"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stats.successful_transactions}</div>
                            <p className="text-xs text-muted-foreground">Completed transactions</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                            <div className="h-4 w-4 rounded-full bg-blue-500"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.total_amount)}</div>
                            <p className="text-xs text-muted-foreground">Total amount processed</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Fees</CardTitle>
                            <div className="h-4 w-4 rounded-full bg-purple-500"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.total_fee)}</div>
                            <p className="text-xs text-muted-foreground">Fees collected</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Transactions Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Transaction History</CardTitle>
                        <CardDescription>
                            Complete list of all transactions from your merchants
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {transactions.data.length === 0 ? (
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                                <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-gray-100">
                                    <TrendingUp className="h-10 w-10 text-gray-400" />
                                </div>
                                <h3 className="mt-4 text-lg font-semibold text-gray-900">No transactions yet</h3>
                                <p className="mt-2 text-sm text-gray-500 max-w-sm">
                                    You haven't received any transactions yet. Generate some sample data to get started.
                                </p>
                                <div className="mt-6">
                                    <Button 
                                        onClick={handleGenerateSampleData}
                                        className="inline-flex items-center"
                                    >
                                        <Plus className="h-4 w-4 mr-2" />
                                        Generate Sample Data
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {transactions.data.map((transaction) => (
                                    <div key={transaction.id} className="border rounded-lg p-4 space-y-2">
                                        <div className="flex items-center justify-between">
                                            <div className="font-medium">{transaction.transaction_reference}</div>
                                            {getStatusBadge(transaction.status)}
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                            <div>
                                                <span className="text-muted-foreground">Customer:</span>
                                                <div className="font-medium">{transaction.customer_name}</div>
                                                {transaction.customer_email && (
                                                    <div className="text-muted-foreground">{transaction.customer_email}</div>
                                                )}
                                            </div>
                                            <div>
                                                <span className="text-muted-foreground">Merchant:</span>
                                                <div className="font-medium">{transaction.merchant.merchant_name}</div>
                                                <div className="text-muted-foreground">{transaction.merchant.merchant_code}</div>
                                            </div>
                                            <div>
                                                <span className="text-muted-foreground">Amount:</span>
                                                <div className="font-medium">{formatCurrency(transaction.amount)}</div>
                                                <div className="text-muted-foreground">Fee: {formatCurrency(transaction.fee)}</div>
                                            </div>
                                        </div>
                                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                                            <span>{transaction.payment_method}</span>
                                            <span>{formatDate(transaction.created_at)}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
