import { Head, router } from '@inertiajs/react';
import { useState } from 'react';
import { Plus, TrendingUp, Search, Filter, Eye, CheckCircle, XCircle, Clock, Calendar, Download } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';

interface Transaction {
    id: number;
    reference_no: string;
    merchant_reference: string | null;
    amount: number;
    fee: number;
    status: 'pending' | 'success' | 'failed';
    customer_name: string;
    customer_email: string | null;
    customer_phone: string | null;
    created_at: string;
    merchant: {
        id: number;
        merchant_name: string;
        merchant_code: string;
    };
}

interface Merchant {
    id: number;
    merchant_name: string;
    merchant_code: string;
}

interface TransactionsData {
    data: Transaction[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

interface Stats {
    total_transactions: number;
    successful_transactions: number;
    pending_transactions: number;
    failed_transactions: number;
    total_amount: number;
    total_fee: number;
}

interface Filters {
    status?: string;
    merchant_id?: string;
    date_from?: string;
    date_to?: string;
    search?: string;
}

interface TransactionIndexProps {
    transactions: TransactionsData;
    merchants: Merchant[];
    filters: Filters;
    stats: Stats;
}

export default function TransactionShadcn({ transactions, merchants, filters, stats }: TransactionIndexProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
    const [selectedMerchant, setSelectedMerchant] = useState(filters.merchant_id || '');
    const [dateFrom, setDateFrom] = useState(filters.date_from || '');
    const [dateTo, setDateTo] = useState(filters.date_to || '');

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'success':
                return (
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Berhasil
                    </Badge>
                );
            case 'pending':
                return (
                    <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
                        <Clock className="h-3 w-3 mr-1" />
                        Pending
                    </Badge>
                );
            case 'failed':
                return (
                    <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
                        <XCircle className="h-3 w-3 mr-1" />
                        Gagal
                    </Badge>
                );
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const handleFilter = () => {
        const params: Record<string, string> = {};
        
        if (searchTerm) params.search = searchTerm;
        if (selectedStatus) params.status = selectedStatus;
        if (selectedMerchant) params.merchant_id = selectedMerchant;
        if (dateFrom) params.date_from = dateFrom;
        if (dateTo) params.date_to = dateTo;

        router.get('/transaction', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedStatus('');
        setSelectedMerchant('');
        setDateFrom('');
        setDateTo('');
        
        router.get('/transaction', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleGenerateSampleData = () => {
        router.post('/sample/generate');
    };

    const getQuickDateFilter = (days: number) => {
        const today = new Date();
        const fromDate = new Date(today);
        fromDate.setDate(today.getDate() - days);
        
        setDateFrom(fromDate.toISOString().split('T')[0]);
        setDateTo(today.toISOString().split('T')[0]);
    };

    return (
        <div className="min-h-screen bg-gray-50">
            <Head title="Daftar Transaksi" />
            
            <div className="container mx-auto p-6 space-y-6">
                {/* Header */}
                <Card>
                    <CardHeader>
                        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                            <div className="space-y-1">
                                <CardTitle className="text-2xl font-bold tracking-tight sm:text-3xl">Daftar Transaksi</CardTitle>
                                <CardDescription className="text-sm sm:text-base">
                                    Monitor semua transaksi dari merchant Anda
                                </CardDescription>
                            </div>
                            {transactions.total === 0 && (
                                <Button onClick={handleGenerateSampleData} className="w-full sm:w-auto">
                                    <Plus className="h-4 w-4 mr-2" />
                                    Generate Sample Data
                                </Button>
                            )}
                        </div>
                    </CardHeader>
                </Card>

                {/* Statistics Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Transaksi</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_transactions}</div>
                            <p className="text-xs text-muted-foreground">Semua transaksi</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Berhasil</CardTitle>
                            <CheckCircle className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stats.successful_transactions}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.total_transactions > 0 ? Math.round((stats.successful_transactions / stats.total_transactions) * 100) : 0}% tingkat keberhasilan
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Pendapatan</CardTitle>
                            <div className="h-4 w-4 rounded-full bg-blue-500"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.total_amount)}</div>
                            <p className="text-xs text-muted-foreground">Dari transaksi berhasil</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Fee</CardTitle>
                            <div className="h-4 w-4 rounded-full bg-purple-500"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.total_fee)}</div>
                            <p className="text-xs text-muted-foreground">Fee yang terkumpul</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Filter Section */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filter & Pencarian
                        </CardTitle>
                        <CardDescription>
                            Filter transaksi berdasarkan tanggal, merchant, status, atau kata kunci pencarian
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
                            <div className="lg:col-span-2">
                                <Label htmlFor="search">Pencarian</Label>
                                <Input
                                    id="search"
                                    placeholder="Cari referensi, customer, email..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full"
                                />
                            </div>
                            <div>
                                <Label htmlFor="status">Status</Label>
                                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Semua Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">Semua Status</SelectItem>
                                        <SelectItem value="success">Berhasil</SelectItem>
                                        <SelectItem value="pending">Pending</SelectItem>
                                        <SelectItem value="failed">Gagal</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label htmlFor="merchant">Merchant</Label>
                                <Select value={selectedMerchant} onValueChange={setSelectedMerchant}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Semua Merchant" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">Semua Merchant</SelectItem>
                                        {merchants.map((merchant) => (
                                            <SelectItem key={merchant.id} value={merchant.id.toString()}>
                                                {merchant.merchant_name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label htmlFor="date-from">Dari Tanggal</Label>
                                <Input
                                    id="date-from"
                                    type="date"
                                    value={dateFrom}
                                    onChange={(e) => setDateFrom(e.target.value)}
                                />
                            </div>
                            <div>
                                <Label htmlFor="date-to">Sampai Tanggal</Label>
                                <Input
                                    id="date-to"
                                    type="date"
                                    value={dateTo}
                                    onChange={(e) => setDateTo(e.target.value)}
                                />
                            </div>
                        </div>
                        
                        <div className="flex flex-col gap-4 mt-4">
                            <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">Filter Cepat:</span>
                                <Button variant="outline" size="sm" onClick={() => getQuickDateFilter(7)}>
                                    <Calendar className="h-3 w-3 mr-1" />
                                    7 Hari
                                </Button>
                                <Button variant="outline" size="sm" onClick={() => getQuickDateFilter(30)}>
                                    <Calendar className="h-3 w-3 mr-1" />
                                    30 Hari
                                </Button>
                                <Button variant="outline" size="sm" onClick={() => getQuickDateFilter(90)}>
                                    <Calendar className="h-3 w-3 mr-1" />
                                    90 Hari
                                </Button>
                            </div>
                            <div className="flex items-center gap-2">
                                <Button onClick={handleFilter}>
                                    <Search className="h-4 w-4 mr-2" />
                                    Terapkan Filter
                                </Button>
                                <Button variant="outline" onClick={clearFilters}>
                                    Hapus Filter
                                </Button>
                                <Button variant="outline" className="ml-auto">
                                    <Download className="h-4 w-4 mr-2" />
                                    Export Excel
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Transactions Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Riwayat Transaksi</CardTitle>
                        <CardDescription>
                            Daftar lengkap semua transaksi dari merchant Anda ({transactions.total} transaksi)
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {transactions.data.length === 0 ? (
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                                <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-gray-100">
                                    <TrendingUp className="h-10 w-10 text-gray-400" />
                                </div>
                                <h3 className="mt-4 text-lg font-semibold text-gray-900">Belum ada transaksi</h3>
                                <p className="mt-2 text-sm text-gray-500 max-w-sm">
                                    Anda belum memiliki transaksi. Generate sample data untuk memulai.
                                </p>
                                <div className="mt-6">
                                    <Button onClick={handleGenerateSampleData} className="inline-flex items-center">
                                        <Plus className="h-4 w-4 mr-2" />
                                        Generate Sample Data
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            <div className="rounded-md border">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Referensi</TableHead>
                                            <TableHead>Customer</TableHead>
                                            <TableHead>Merchant</TableHead>
                                            <TableHead>Jumlah</TableHead>
                                            <TableHead>Fee</TableHead>
                                            <TableHead>Status</TableHead>
                                            <TableHead>Tanggal</TableHead>
                                            <TableHead className="text-right">Aksi</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {transactions.data.map((transaction) => (
                                            <TableRow key={transaction.id} className="hover:bg-muted/50">
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium">{transaction.reference_no}</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            {transaction.merchant_reference || 'No merchant ref'}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium">{transaction.customer_name}</div>
                                                        {transaction.customer_email && (
                                                            <div className="text-sm text-muted-foreground">
                                                                {transaction.customer_email}
                                                            </div>
                                                        )}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium">{transaction.merchant.merchant_name}</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            {transaction.merchant.merchant_code}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="font-medium">
                                                        {formatCurrency(transaction.amount)}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="text-sm">
                                                        {formatCurrency(transaction.fee)}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    {getStatusBadge(transaction.status)}
                                                </TableCell>
                                                <TableCell className="text-muted-foreground">
                                                    {formatDate(transaction.created_at)}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <Button variant="outline" size="sm">
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
