import { Head } from '@inertiajs/react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Plus, CheckCircle, XCircle, Clock } from 'lucide-react';

interface Transaction {
    id: number;
    reference_no: string;
    merchant_reference: string | null;
    amount: number;
    fee: number;
    status: 'pending' | 'success' | 'failed';
    customer_name: string;
    customer_email: string | null;
    created_at: string;
    merchant: {
        merchant_name: string;
        merchant_code: string;
    };
}

interface Props {
    transactions: {
        data: Transaction[];
        total: number;
    };
    stats: {
        total_transactions: number;
        successful_transactions: number;
        total_amount: number;
        total_fee: number;
    };
}

export default function TransactionMinimal({ transactions, stats }: Props) {
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'success':
                return (
                    <Badge className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Berhasil
                    </Badge>
                );
            case 'pending':
                return (
                    <Badge className="bg-yellow-100 text-yellow-800">
                        <Clock className="h-3 w-3 mr-1" />
                        Pending
                    </Badge>
                );
            case 'failed':
                return (
                    <Badge className="bg-red-100 text-red-800">
                        <XCircle className="h-3 w-3 mr-1" />
                        Gagal
                    </Badge>
                );
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    return (
        <div className="min-h-screen bg-gray-50">
            <Head title="Daftar Transaksi" />
            
            <div className="container mx-auto p-6 space-y-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="space-y-1">
                        <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">Daftar Transaksi</h1>
                        <p className="text-sm text-muted-foreground sm:text-base">
                            Monitor semua transaksi dari merchant Anda
                        </p>
                    </div>
                    {transactions.total === 0 && (
                        <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Generate Sample Data
                        </Button>
                    )}
                </div>

                {/* Statistics Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Transaksi</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_transactions}</div>
                            <p className="text-xs text-muted-foreground">Semua transaksi</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Berhasil</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stats.successful_transactions}</div>
                            <p className="text-xs text-muted-foreground">Transaksi berhasil</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Pendapatan</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.total_amount)}</div>
                            <p className="text-xs text-muted-foreground">Dari transaksi berhasil</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Fee</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.total_fee)}</div>
                            <p className="text-xs text-muted-foreground">Fee yang terkumpul</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Transactions Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Riwayat Transaksi</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {transactions.data.length === 0 ? (
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                                <h3 className="mt-4 text-lg font-semibold text-gray-900">Belum ada transaksi</h3>
                                <p className="mt-2 text-sm text-gray-500 max-w-sm">
                                    Anda belum memiliki transaksi. Generate sample data untuk memulai.
                                </p>
                                <div className="mt-6">
                                    <Button className="inline-flex items-center">
                                        <Plus className="h-4 w-4 mr-2" />
                                        Generate Sample Data
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            <div className="rounded-md border">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Referensi</TableHead>
                                            <TableHead>Customer</TableHead>
                                            <TableHead>Merchant</TableHead>
                                            <TableHead>Jumlah</TableHead>
                                            <TableHead>Fee</TableHead>
                                            <TableHead>Status</TableHead>
                                            <TableHead>Tanggal</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {transactions.data.map((transaction) => (
                                            <TableRow key={transaction.id} className="hover:bg-muted/50">
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium">{transaction.reference_no}</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            {transaction.merchant_reference || 'No merchant ref'}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium">{transaction.customer_name}</div>
                                                        {transaction.customer_email && (
                                                            <div className="text-sm text-muted-foreground">
                                                                {transaction.customer_email}
                                                            </div>
                                                        )}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium">{transaction.merchant.merchant_name}</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            {transaction.merchant.merchant_code}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="font-medium">
                                                        {formatCurrency(transaction.amount)}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="text-sm">
                                                        {formatCurrency(transaction.fee)}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    {getStatusBadge(transaction.status)}
                                                </TableCell>
                                                <TableCell className="text-muted-foreground">
                                                    {formatDate(transaction.created_at)}
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
