import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { Plus, TrendingUp } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';

interface Transaction {
    id: number;
    reference_no: string;
    merchant_reference: string | null;
    amount: number;
    fee: number;
    status: 'pending' | 'success' | 'failed';
    customer_name: string;
    customer_email: string | null;
    customer_phone: string | null;
    created_at: string;
    merchant: {
        id: number;
        merchant_name: string;
        merchant_code: string;
    };
}

interface Merchant {
    id: number;
    merchant_name: string;
    merchant_code: string;
}

interface TransactionsData {
    data: Transaction[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

interface Stats {
    total_transactions: number;
    successful_transactions: number;
    pending_transactions: number;
    failed_transactions: number;
    total_amount: number;
    total_fee: number;
}

interface Filters {
    status?: string;
    merchant_id?: string;
    date_from?: string;
    date_to?: string;
    search?: string;
}

interface TransactionIndexProps {
    transactions: TransactionsData;
    merchants: Merchant[];
    filters: Filters;
    stats: Stats;
}

const breadcrumbItems: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Daftar Transaksi', href: '/transaction' },
];

export default function TransactionIndex({ transactions, merchants, filters, stats }: TransactionIndexProps) {
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const handleGenerateSampleData = () => {
        router.post(route('sample.generate'));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbItems}>
            <Head title="Daftar Transaksi" />
            
            <div className="space-y-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="space-y-1">
                        <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">Daftar Transaksi</h1>
                        <p className="text-sm text-muted-foreground sm:text-base">
                            Monitor semua transaksi dari merchant Anda
                        </p>
                    </div>
                    {transactions.total === 0 && (
                        <Button onClick={handleGenerateSampleData} className="w-full sm:w-auto">
                            <Plus className="h-4 w-4 mr-2" />
                            Generate Sample Data
                        </Button>
                    )}
                </div>

                {/* Statistics Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Transaksi</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_transactions}</div>
                            <p className="text-xs text-muted-foreground">Semua transaksi</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Berhasil</CardTitle>
                            <div className="h-4 w-4 rounded-full bg-green-500"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stats.successful_transactions}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.total_transactions > 0 ? Math.round((stats.successful_transactions / stats.total_transactions) * 100) : 0}% tingkat keberhasilan
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Pendapatan</CardTitle>
                            <div className="h-4 w-4 rounded-full bg-blue-500"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.total_amount)}</div>
                            <p className="text-xs text-muted-foreground">Dari transaksi berhasil</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Fee</CardTitle>
                            <div className="h-4 w-4 rounded-full bg-purple-500"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.total_fee)}</div>
                            <p className="text-xs text-muted-foreground">Fee yang terkumpul</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Transactions List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Riwayat Transaksi</CardTitle>
                        <CardDescription>
                            Daftar lengkap semua transaksi dari merchant Anda
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {transactions.data.length === 0 ? (
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                                <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-gray-100">
                                    <TrendingUp className="h-10 w-10 text-gray-400" />
                                </div>
                                <h3 className="mt-4 text-lg font-semibold text-gray-900">Belum ada transaksi</h3>
                                <p className="mt-2 text-sm text-gray-500 max-w-sm">
                                    Anda belum memiliki transaksi. Generate sample data untuk memulai.
                                </p>
                                <div className="mt-6">
                                    <Button onClick={handleGenerateSampleData} className="inline-flex items-center">
                                        <Plus className="h-4 w-4 mr-2" />
                                        Generate Sample Data
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {transactions.data.map((transaction) => (
                                    <div key={transaction.id} className="border rounded-lg p-4 space-y-2">
                                        <div className="flex items-center justify-between">
                                            <div className="font-medium">{transaction.reference_no}</div>
                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                transaction.status === 'success' ? 'bg-green-100 text-green-800' :
                                                transaction.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                'bg-red-100 text-red-800'
                                            }`}>
                                                {transaction.status === 'success' ? 'Berhasil' :
                                                 transaction.status === 'pending' ? 'Pending' : 'Gagal'}
                                            </span>
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                            <div>
                                                <span className="text-muted-foreground">Customer:</span>
                                                <div className="font-medium">{transaction.customer_name}</div>
                                                {transaction.customer_email && (
                                                    <div className="text-muted-foreground">{transaction.customer_email}</div>
                                                )}
                                            </div>
                                            <div>
                                                <span className="text-muted-foreground">Merchant:</span>
                                                <div className="font-medium">{transaction.merchant.merchant_name}</div>
                                                <div className="text-muted-foreground">{transaction.merchant.merchant_code}</div>
                                            </div>
                                            <div>
                                                <span className="text-muted-foreground">Amount:</span>
                                                <div className="font-medium">{formatCurrency(transaction.amount)}</div>
                                                <div className="text-muted-foreground">Fee: {formatCurrency(transaction.fee)}</div>
                                            </div>
                                        </div>
                                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                                            <span>{transaction.merchant_reference || 'No merchant ref'}</span>
                                            <span>{formatDate(transaction.created_at)}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
