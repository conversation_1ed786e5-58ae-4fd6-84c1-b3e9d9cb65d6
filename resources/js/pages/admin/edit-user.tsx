import { type BreadcrumbItem } from '@/types';
import { Transition } from '@headlessui/react';
import { Head, Link, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { ArrowLeft, User, Mail, Phone, Building, CreditCard, UserCheck, Calendar } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import InputError from '@/components/input-error';
import AppLayout from '@/layouts/app-layout';

interface User {
    id: number;
    name: string;
    email: string;
    phone: string;
    bank_name: string | null;
    account_number: string | null;
    account_holder_name: string | null;
    withdrawal_limit: number | null;
    email_verified_at: string | null;
    created_at: string;
}

interface EditUserProps {
    user: User;
}

type UserForm = {
    name: string;
    email: string;
    phone: string;
    bank_name: string;
    account_number: string;
    account_holder_name: string;
    withdrawal_limit: string;
};

// Indonesian Banks List
const indonesianBanks = [
    'Bank Central Asia (BCA)',
    'Bank Mandiri',
    'Bank Rakyat Indonesia (BRI)',
    'Bank Negara Indonesia (BNI)',
    'Bank CIMB Niaga',
    'Bank Danamon',
    'Bank Permata',
    'Bank Maybank Indonesia',
    'Bank OCBC NISP',
    'Bank Panin',
    'Bank BTPN',
    'Bank Mega',
    'Bank Bukopin',
    'Bank Sinarmas',
    'Bank UOB Indonesia',
    'Bank Commonwealth',
    'Bank QNB Indonesia',
    'Bank Woori Saudara',
    'Bank HSBC Indonesia',
    'Bank Citibank',
    'Bank Standard Chartered',
    'Bank DBS Indonesia',
    'Bank Mizuho Indonesia',
    'Bank Resona Perdania',
    'Bank Sumitomo Mitsui Indonesia',
    'Bank Capital Indonesia',
    'Bank Bumi Arta',
    'Bank Ekonomi Raharja',
    'Bank Antardaerah',
    'Bank Artha Graha Internasional',
    'Bank Credit Agricole Indosuez',
    'Bank Windu Kentjana International',
    'Bank Multicor',
    'Bank Pundi Indonesia',
    'Bank Yudha Bhakti',
    'Bank Mitraniaga',
    'Bank Royal Indonesia',
    'Bank National Nobu',
    'Bank INA Perdana',
    'Bank Harfa',
    'Bank Kesawan',
    'Bank Tabungan Negara (BTN)',
    'Bank Syariah Indonesia (BSI)',
    'Bank Muamalat',
    'Bank Syariah Mandiri',
    'Bank BRI Syariah',
    'Bank BNI Syariah',
    'Bank BCA Syariah',
    'Bank Panin Dubai Syariah',
    'Bank Victoria Syariah',
    'Bank BTPN Syariah',
    'Bank Bukopin Syariah',
    'Bank Mega Syariah',
    'Bank CIMB Niaga Syariah',
    'Bank Danamon Syariah',
    'Bank Maybank Syariah',
    'Bank OCBC NISP Syariah',
    'Bank Permata Syariah',
    'Bank DKI',
    'Bank Jateng',
    'Bank Jatim',
    'Bank Jabar Banten',
    'Bank Sumut',
    'Bank Sumbar',
    'Bank Riau Kepri',
    'Bank Jambi',
    'Bank Sumsel Babel',
    'Bank Lampung',
    'Bank Kalbar',
    'Bank Kalteng',
    'Bank Kalsel',
    'Bank Kaltim',
    'Bank Kaltara',
    'Bank Sulselbar',
    'Bank Sulteng',
    'Bank Sultra',
    'Bank Sulut Gorontalo',
    'Bank Maluku Malut',
    'Bank Papua',
    'Bank Bali',
    'Bank NTB',
    'Bank NTT',
    'Bank Yogyakarta',
    'Bank Nagari',
    'Bank Bengkulu',
    'Bank Aceh Syariah',
    'Lainnya'
];

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/DashboardAdmin',
    },
    {
        title: 'User Management',
        href: '/menuadmin',
    },
    {
        title: 'Edit User',
        href: '#',
    },
];

export default function EditUser({ user }: EditUserProps) {
    const { data, setData, patch, errors, processing, recentlySuccessful } = useForm<Required<UserForm>>({
        name: user.name,
        email: user.email,
        phone: user.phone,
        bank_name: user.bank_name || '',
        account_number: user.account_number || '',
        account_holder_name: user.account_holder_name || '',
        withdrawal_limit: user.withdrawal_limit ? user.withdrawal_limit.toString() : '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        patch(route('admin.users.update', user.id), {
            preserveScroll: true,
        });
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Edit User - ${user.name}`} />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="outline" size="sm" asChild>
                            <Link href={route('admin.users.index')}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Users
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Edit User</h1>
                            <p className="text-muted-foreground">
                                Update user information and bank account details
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Badge variant="secondary">
                            ID: #{user.id}
                        </Badge>
                        {user.email_verified_at ? (
                            <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
                                <UserCheck className="h-3 w-3 mr-1" />
                                Verified
                            </Badge>
                        ) : (
                            <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
                                <Mail className="h-3 w-3 mr-1" />
                                Unverified
                            </Badge>
                        )}
                    </div>
                </div>

                {/* User Info Card */}
                <div className="rounded-lg border bg-card p-6">
                    <div className="flex items-center gap-4 mb-4">
                        <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                            <User className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                            <h3 className="text-lg font-semibold">{user.name}</h3>
                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                Joined {formatDate(user.created_at)}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Edit Form */}
                <form onSubmit={submit} className="space-y-8">
                    {/* Personal Information */}
                    <div className="rounded-lg border bg-card p-6">
                        <div className="flex items-center gap-2 mb-6">
                            <User className="h-5 w-5 text-primary" />
                            <h2 className="text-xl font-semibold">Personal Information</h2>
                        </div>
                        
                        <div className="grid gap-6 md:grid-cols-2">
                            <div className="grid gap-2">
                                <Label htmlFor="name">Full Name</Label>
                                <Input
                                    id="name"
                                    className="mt-1 block w-full"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    required
                                    autoComplete="name"
                                />
                                <InputError className="mt-2" message={errors.name} />
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="email">Email Address</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    className="mt-1 block w-full"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    required
                                    autoComplete="username"
                                />
                                <InputError className="mt-2" message={errors.email} />
                            </div>

                            <div className="grid gap-2 md:col-span-2">
                                <Label htmlFor="phone">Phone Number</Label>
                                <Input
                                    id="phone"
                                    type="tel"
                                    className="mt-1 block w-full"
                                    value={data.phone}
                                    onChange={(e) => {
                                        let value = e.target.value;
                                        // Ensure it always starts with +62
                                        if (!value.startsWith('+62')) {
                                            value = '+62' + value.replace(/^\+?62?/, '');
                                        }
                                        // Remove any non-digit characters except +
                                        value = value.replace(/[^\d+]/g, '');
                                        // Limit length to +62 + 13 digits max
                                        if (value.length > 16) {
                                            value = value.substring(0, 16);
                                        }
                                        setData('phone', value);
                                    }}
                                    required
                                    autoComplete="tel"
                                    placeholder="+***********"
                                />
                                <InputError className="mt-2" message={errors.phone} />
                            </div>
                        </div>
                    </div>

                    {/* Bank Account Details */}
                    <div className="rounded-lg border bg-card p-6">
                        <div className="flex items-center gap-2 mb-6">
                            <Building className="h-5 w-5 text-primary" />
                            <h2 className="text-xl font-semibold">Bank Account Details</h2>
                        </div>

                        <div className="grid gap-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="bank_name">Bank Name</Label>
                                    <Select value={data.bank_name} onValueChange={(value) => setData('bank_name', value)}>
                                        <SelectTrigger className="w-full">
                                            <SelectValue placeholder="Select a bank" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {indonesianBanks.map((bank) => (
                                                <SelectItem key={bank} value={bank}>
                                                    {bank}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError className="mt-2" message={errors.bank_name} />
                                </div>

                                <div className="grid gap-2">
                                    <Label htmlFor="account_number">Account Number</Label>
                                    <Input
                                        id="account_number"
                                        className="mt-1 block w-full"
                                        value={data.account_number}
                                        onChange={(e) => {
                                            // Only allow numbers
                                            const value = e.target.value.replace(/[^\d]/g, '');
                                            setData('account_number', value);
                                        }}
                                        autoComplete="off"
                                        placeholder="e.g., **********"
                                    />
                                    <InputError className="mt-2" message={errors.account_number} />
                                </div>
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="account_holder_name">Account Holder Name</Label>
                                <Input
                                    id="account_holder_name"
                                    className="mt-1 block w-full"
                                    value={data.account_holder_name}
                                    onChange={(e) => setData('account_holder_name', e.target.value)}
                                    autoComplete="off"
                                    placeholder="Full name as registered in bank"
                                />
                                <InputError className="mt-2" message={errors.account_holder_name} />
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="withdrawal_limit">Withdrawal Limit (IDR)</Label>
                                <Input
                                    id="withdrawal_limit"
                                    className="mt-1 block w-full"
                                    value={data.withdrawal_limit}
                                    onChange={(e) => {
                                        // Only allow numbers and format as currency
                                        const value = e.target.value.replace(/[^\d]/g, '');
                                        setData('withdrawal_limit', value);
                                    }}
                                    autoComplete="off"
                                    placeholder="e.g., 5000000"
                                />
                                <InputError className="mt-2" message={errors.withdrawal_limit} />
                                <p className="text-sm text-muted-foreground">
                                    Maximum amount user can withdraw per transaction
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Save Button */}
                    <div className="flex items-center justify-between rounded-lg border bg-card p-6">
                        <div>
                            <h3 className="font-medium">Save Changes</h3>
                            <p className="text-sm text-muted-foreground">
                                Update user information and bank account details
                            </p>
                        </div>
                        <div className="flex items-center gap-4">
                            <Transition
                                show={recentlySuccessful}
                                enter="transition ease-in-out"
                                enterFrom="opacity-0"
                                leave="transition ease-in-out"
                                leaveTo="opacity-0"
                            >
                                <p className="text-sm text-green-600 flex items-center gap-1">
                                    <UserCheck className="h-4 w-4" />
                                    Saved successfully
                                </p>
                            </Transition>
                            <Button disabled={processing} className="min-w-[120px]">
                                {processing ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        Saving...
                                    </>
                                ) : (
                                    <>
                                        <CreditCard className="h-4 w-4 mr-2" />
                                        Save Changes
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
