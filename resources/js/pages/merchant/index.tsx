import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { useState } from 'react';
import { Plus, Store, Eye, Edit, TrendingUp, Users, DollarSign, Search, Filter, RefreshCw } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import DeleteMerchantDialog from '@/components/delete-merchant-dialog';
import AppLayout from '@/layouts/app-layout';

interface Merchant {
    id: number;
    merchant_code: string;
    merchant_name: string;
    merchant_type: string | null;
    description: string | null;
    contact_email: string | null;
    contact_phone: string | null;
    is_active: boolean;
    created_at: string;
    transactions_count: number;
    total_transaction_amount: number;
    successful_transactions: number;
    total_revenue: number;
}

interface MerchantsData {
    data: Merchant[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface Filters {
    status?: string;
    merchant_type?: string;
    search?: string;
    per_page?: number;
}

interface Stats {
    total_merchants: number;
    active_merchants: number;
    inactive_merchants: number;
    total_revenue: number;
}

interface MerchantIndexProps {
    merchants: MerchantsData;
    filters: Filters;
    stats: Stats;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Merchant',
        href: '/merchant',
    },
];

export default function MerchantIndex({ merchants, filters, stats }: MerchantIndexProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
    const [selectedType, setSelectedType] = useState(filters.merchant_type || '');
    const [currentPerPage, setCurrentPerPage] = useState(filters.per_page || merchants.per_page || 25);
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(amount);
    };



    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const getMerchantTypeLabel = (type: string | null) => {
        const types: Record<string, string> = {
            'online_store': 'Online Store',
            'restaurant': 'Restaurant',
            'service': 'Service',
            'retail': 'Retail',
            'digital_service': 'Digital Service',
        };
        return type ? types[type] || type : 'Not specified';
    };

    const handleFilter = () => {
        const params: Record<string, string> = {};

        if (searchTerm) params.search = searchTerm;
        if (selectedStatus) params.status = selectedStatus;
        if (selectedType) params.merchant_type = selectedType;
        if (currentPerPage !== 25) params.per_page = currentPerPage.toString();

        router.get('/merchant', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedStatus('');
        setSelectedType('');
        setCurrentPerPage(25);

        router.get('/merchant', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const changePerPage = (perPage: number) => {
        setCurrentPerPage(perPage);

        const params: Record<string, string> = {};

        if (searchTerm) params.search = searchTerm;
        if (selectedStatus) params.status = selectedStatus;
        if (selectedType) params.merchant_type = selectedType;
        params.per_page = perPage.toString();

        router.get('/merchant', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleRefresh = () => {
        router.reload();
    };

    // Use stats from backend
    const totalMerchants = stats.total_merchants;
    const activeMerchants = stats.active_merchants;
    const totalTransactions = merchants.data.reduce((sum, m) => sum + m.transactions_count, 0);
    const totalRevenue = stats.total_revenue;

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Merchant Management" />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="space-y-1">
                        <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">Merchant Management</h1>
                        <p className="text-sm text-muted-foreground sm:text-base">
                            Manage your merchants and track their performance
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" onClick={handleRefresh} size="sm">
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Refresh
                        </Button>
                        <Button asChild className="w-full sm:w-auto">
                            <Link href={route('merchant.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                <span className="hidden sm:inline">Add Merchant</span>
                                <span className="sm:hidden">Add</span>
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Merchants</CardTitle>
                            <Store className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{totalMerchants}</div>
                            <p className="text-xs text-muted-foreground">
                                {activeMerchants} active
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{totalTransactions}</div>
                            <p className="text-xs text-muted-foreground">
                                Across all merchants
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
                            <p className="text-xs text-muted-foreground">
                                From successful transactions
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Rate</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {totalMerchants > 0 ? Math.round((activeMerchants / totalMerchants) * 100) : 0}%
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Merchants currently active
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Filter Section */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filter & Pencarian
                        </CardTitle>
                        <CardDescription>
                            Filter merchant berdasarkan status, tipe, atau kata kunci pencarian
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <div>
                                <Label htmlFor="search">Pencarian</Label>
                                <Input
                                    id="search"
                                    placeholder="Cari nama, kode, email, phone..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full"
                                />
                            </div>
                            <div>
                                <Label htmlFor="status">Status</Label>
                                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Semua Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">Semua Status</SelectItem>
                                        <SelectItem value="active">Active</SelectItem>
                                        <SelectItem value="inactive">Inactive</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label htmlFor="type">Tipe Merchant</Label>
                                <Select value={selectedType} onValueChange={setSelectedType}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Semua Tipe" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">Semua Tipe</SelectItem>
                                        <SelectItem value="online_store">Online Store</SelectItem>
                                        <SelectItem value="restaurant">Restaurant</SelectItem>
                                        <SelectItem value="retail">Retail</SelectItem>
                                        <SelectItem value="service">Service</SelectItem>
                                        <SelectItem value="digital_service">Digital Service</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label htmlFor="per-page">Per Halaman</Label>
                                <Select value={currentPerPage.toString()} onValueChange={(value) => changePerPage(parseInt(value))}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="10">10</SelectItem>
                                        <SelectItem value="25">25</SelectItem>
                                        <SelectItem value="50">50</SelectItem>
                                        <SelectItem value="100">100</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        <div className="flex items-center gap-2 mt-4">
                            <Button onClick={handleFilter}>
                                <Search className="h-4 w-4 mr-2" />
                                Terapkan Filter
                            </Button>
                            <Button variant="outline" onClick={clearFilters}>
                                Hapus Filter
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Merchants Table */}
                <Card>
                    <CardHeader>
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                            <div>
                                <CardTitle>Your Merchants</CardTitle>
                                <CardDescription>
                                    A list of all your registered merchants ({merchants.total} merchants)
                                    {merchants.last_page > 1 && (
                                        <span className="ml-2 text-gray-500">
                                            - Halaman {merchants.current_page} dari {merchants.last_page}
                                        </span>
                                    )}
                                </CardDescription>
                            </div>
                            {merchants.total > 0 && (
                                <div className="text-sm text-gray-500">
                                    {merchants.per_page} per halaman
                                </div>
                            )}
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Merchant</TableHead>
                                        <TableHead>Code</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Transactions</TableHead>
                                        <TableHead>Revenue</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {merchants.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={8} className="h-24 text-center">
                                                <div className="flex flex-col items-center gap-2">
                                                    <Store className="h-8 w-8 text-muted-foreground" />
                                                    <p className="text-muted-foreground">No merchants found</p>
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('merchant.create')}>
                                                            <Plus className="h-4 w-4 mr-2" />
                                                            <span className="hidden sm:inline">Create your first merchant</span>
                                                            <span className="sm:hidden">Create Merchant</span>
                                                        </Link>
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        merchants.data.map((merchant) => (
                                            <TableRow key={merchant.id}>
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium">{merchant.merchant_name}</div>
                                                        {merchant.description && (
                                                            <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                                                                {merchant.description}
                                                            </div>
                                                        )}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <code className="text-sm bg-muted px-2 py-1 rounded">
                                                        {merchant.merchant_code}
                                                    </code>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant="outline">
                                                        {getMerchantTypeLabel(merchant.merchant_type)}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    {merchant.is_active ? (
                                                        <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
                                                            Active
                                                        </Badge>
                                                    ) : (
                                                        <Badge variant="secondary" className="bg-red-100 text-red-800 hover:bg-red-100">
                                                            Inactive
                                                        </Badge>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="text-sm">
                                                        {merchant.transactions_count} transactions
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="text-sm font-medium">
                                                        {formatCurrency(merchant.total_transaction_amount)}
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-muted-foreground">
                                                    {formatDate(merchant.created_at)}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-gray-100">
                                                                <span className="sr-only">Open actions menu</span>
                                                                <Edit className="h-4 w-4" />
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end">
                                                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                            <DropdownMenuItem
                                                                onClick={() => router.visit(route('merchant.show', merchant.id))}
                                                                className="cursor-pointer"
                                                            >
                                                                <Eye className="mr-2 h-4 w-4" />
                                                                View Details
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem
                                                                onClick={() => router.visit(route('merchant.edit', merchant.id))}
                                                                className="cursor-pointer"
                                                            >
                                                                <Edit className="mr-2 h-4 w-4" />
                                                                Edit Merchant
                                                            </DropdownMenuItem>
                                                            <DropdownMenuSeparator />
                                                            <DeleteMerchantDialog
                                                                merchantId={merchant.id}
                                                                merchantName={merchant.merchant_name}
                                                            />
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>

                {/* Pagination */}
                {merchants.last_page > 1 && (
                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
                                <div className="text-sm text-gray-700">
                                    Menampilkan {((merchants.current_page - 1) * merchants.per_page) + 1} sampai{' '}
                                    {Math.min(merchants.current_page * merchants.per_page, merchants.total)} dari{' '}
                                    {merchants.total} hasil
                                </div>

                                <div className="flex items-center gap-1">
                                    {merchants.links.map((link, index) => {
                                        if (!link.url) {
                                            return (
                                                <span
                                                    key={index}
                                                    className="px-3 py-2 text-sm text-gray-400 cursor-not-allowed"
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            );
                                        }

                                        return (
                                            <button
                                                key={index}
                                                onClick={() => router.get(link.url!)}
                                                className={`px-3 py-2 text-sm rounded-md transition-colors ${
                                                    link.active
                                                        ? 'bg-blue-600 text-white shadow-sm'
                                                        : 'text-gray-700 hover:bg-gray-100 border border-gray-300 hover:border-gray-400'
                                                }`}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        );
                                    })}
                                </div>
                            </div>

                            {/* Mobile-friendly page info */}
                            <div className="mt-4 pt-4 border-t border-gray-200 sm:hidden">
                                <div className="text-center text-sm text-gray-600">
                                    Halaman {merchants.current_page} dari {merchants.last_page}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
