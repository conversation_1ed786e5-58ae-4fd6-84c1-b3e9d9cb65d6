import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Plus, Store, Eye, Edit, Trash2, TrendingUp, Users, DollarSign, MoreHorizontal } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import DeleteMerchantDialog from '@/components/delete-merchant-dialog';
import AppLayout from '@/layouts/app-layout';

interface Merchant {
    id: number;
    merchant_code: string;
    merchant_name: string;
    merchant_type: string | null;
    description: string | null;
    is_active: boolean;
    created_at: string;
    transactions_count: number;
    total_transaction_amount: number;
}

interface MerchantsData {
    data: Merchant[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface MerchantIndexProps {
    merchants: MerchantsData;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Merchant',
        href: '/merchant',
    },
];

export default function MerchantIndex({ merchants }: MerchantIndexProps) {
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(amount);
    };



    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const getMerchantTypeLabel = (type: string | null) => {
        const types: Record<string, string> = {
            'online_store': 'Online Store',
            'restaurant': 'Restaurant',
            'service': 'Service',
            'retail': 'Retail',
            'digital_service': 'Digital Service',
        };
        return type ? types[type] || type : 'Not specified';
    };

    // Calculate summary stats
    const totalMerchants = merchants.total;
    const activeMerchants = merchants.data.filter(m => m.is_active).length;
    const totalTransactions = merchants.data.reduce((sum, m) => sum + m.transactions_count, 0);
    const totalRevenue = merchants.data.reduce((sum, m) => sum + m.total_transaction_amount, 0);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Merchant Management" />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div className="space-y-1">
                        <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">Merchant Management</h1>
                        <p className="text-sm text-muted-foreground sm:text-base">
                            Manage your merchants and track their performance
                        </p>
                    </div>
                    <div className="flex shrink-0">
                        <Button asChild className="w-full sm:w-auto">
                            <Link href={route('merchant.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                <span className="hidden sm:inline">Add Merchant</span>
                                <span className="sm:hidden">Add</span>
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Merchants</CardTitle>
                            <Store className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{totalMerchants}</div>
                            <p className="text-xs text-muted-foreground">
                                {activeMerchants} active
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{totalTransactions}</div>
                            <p className="text-xs text-muted-foreground">
                                Across all merchants
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
                            <p className="text-xs text-muted-foreground">
                                From successful transactions
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Rate</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {totalMerchants > 0 ? Math.round((activeMerchants / totalMerchants) * 100) : 0}%
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Merchants currently active
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Merchants Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Your Merchants</CardTitle>
                        <CardDescription>
                            A list of all your registered merchants
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Merchant</TableHead>
                                        <TableHead>Code</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Transactions</TableHead>
                                        <TableHead>Revenue</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {merchants.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={8} className="h-24 text-center">
                                                <div className="flex flex-col items-center gap-2">
                                                    <Store className="h-8 w-8 text-muted-foreground" />
                                                    <p className="text-muted-foreground">No merchants found</p>
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('merchant.create')}>
                                                            <Plus className="h-4 w-4 mr-2" />
                                                            <span className="hidden sm:inline">Create your first merchant</span>
                                                            <span className="sm:hidden">Create Merchant</span>
                                                        </Link>
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        merchants.data.map((merchant) => (
                                            <TableRow key={merchant.id}>
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium">{merchant.merchant_name}</div>
                                                        {merchant.description && (
                                                            <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                                                                {merchant.description}
                                                            </div>
                                                        )}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <code className="text-sm bg-muted px-2 py-1 rounded">
                                                        {merchant.merchant_code}
                                                    </code>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant="outline">
                                                        {getMerchantTypeLabel(merchant.merchant_type)}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    {merchant.is_active ? (
                                                        <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
                                                            Active
                                                        </Badge>
                                                    ) : (
                                                        <Badge variant="secondary" className="bg-red-100 text-red-800 hover:bg-red-100">
                                                            Inactive
                                                        </Badge>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="text-sm">
                                                        {merchant.transactions_count} transactions
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="text-sm font-medium">
                                                        {formatCurrency(merchant.total_transaction_amount)}
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-muted-foreground">
                                                    {formatDate(merchant.created_at)}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="outline" size="sm" className="h-9 px-3 border-2 hover:bg-gray-50">
                                                                <MoreHorizontal className="h-4 w-4 mr-2" />
                                                                <span className="font-medium">Actions</span>
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end">
                                                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                            <DropdownMenuItem
                                                                onClick={() => router.visit(route('merchant.show', merchant.id))}
                                                                className="cursor-pointer"
                                                            >
                                                                <Eye className="mr-2 h-4 w-4" />
                                                                View Details
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem
                                                                onClick={() => router.visit(route('merchant.edit', merchant.id))}
                                                                className="cursor-pointer"
                                                            >
                                                                <Edit className="mr-2 h-4 w-4" />
                                                                Edit Merchant
                                                            </DropdownMenuItem>
                                                            <DropdownMenuSeparator />
                                                            <DeleteMerchantDialog
                                                                merchantId={merchant.id}
                                                                merchantName={merchant.merchant_name}
                                                            />
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>

                {/* Pagination */}
                {merchants.last_page > 1 && (
                    <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">
                            Showing {((merchants.current_page - 1) * merchants.per_page) + 1} to{' '}
                            {Math.min(merchants.current_page * merchants.per_page, merchants.total)} of{' '}
                            {merchants.total} results
                        </div>
                        <div className="flex items-center gap-2">
                            {merchants.links.map((link, index) => {
                                if (!link.url) {
                                    return (
                                        <Button
                                            key={index}
                                            variant="outline"
                                            size="sm"
                                            disabled
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    );
                                }

                                return (
                                    <Button
                                        key={index}
                                        variant={link.active ? "default" : "outline"}
                                        size="sm"
                                        asChild
                                    >
                                        <Link
                                            href={link.url}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    </Button>
                                );
                            })}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
