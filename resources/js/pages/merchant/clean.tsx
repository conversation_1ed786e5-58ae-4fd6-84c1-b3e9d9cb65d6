import { Head, router, Link } from '@inertiajs/react';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Plus, Store, Eye, Edit, TrendingUp, Users, DollarSign, Search, Filter, RefreshCw, Trash2, Alert<PERSON>riangle } from 'lucide-react';

interface Merchant {
    id: number;
    merchant_code: string;
    merchant_name: string;
    merchant_type: string | null;
    description: string | null;
    contact_email: string | null;
    contact_phone: string | null;
    is_active: boolean;
    created_at: string;
    transactions_count: number;
    total_transaction_amount: number;
    successful_transactions: number;
    total_revenue: number;
}

interface MerchantsData {
    data: Merchant[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface Filters {
    status?: string;
    merchant_type?: string;
    search?: string;
    per_page?: number;
}

interface Stats {
    total_merchants: number;
    active_merchants: number;
    inactive_merchants: number;
    total_revenue: number;
}

interface MerchantIndexProps {
    merchants: MerchantsData;
    filters: Filters;
    stats: Stats;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Merchant',
        href: '/merchant',
    },
];

export default function MerchantClean({ merchants, filters, stats }: MerchantIndexProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
    const [selectedType, setSelectedType] = useState(filters.merchant_type || '');
    const [currentPerPage, setCurrentPerPage] = useState(filters.per_page || merchants.per_page || 25);
    const [deleteModal, setDeleteModal] = useState<{ isOpen: boolean; merchant: Merchant | null }>({
        isOpen: false,
        merchant: null
    });

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const getMerchantTypeLabel = (type: string | null) => {
        const types: Record<string, string> = {
            'online_store': 'Online Store',
            'restaurant': 'Restaurant',
            'service': 'Service',
            'retail': 'Retail',
            'digital_service': 'Digital Service',
        };
        return type ? types[type] || type : 'Not specified';
    };

    const handleFilter = () => {
        const params: Record<string, string> = {};
        
        if (searchTerm) params.search = searchTerm;
        if (selectedStatus) params.status = selectedStatus;
        if (selectedType) params.merchant_type = selectedType;
        if (currentPerPage !== 25) params.per_page = currentPerPage.toString();

        router.get('/merchant', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedStatus('');
        setSelectedType('');
        setCurrentPerPage(25);
        
        router.get('/merchant', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const changePerPage = (perPage: number) => {
        setCurrentPerPage(perPage);
        
        const params: Record<string, string> = {};
        
        if (searchTerm) params.search = searchTerm;
        if (selectedStatus) params.status = selectedStatus;
        if (selectedType) params.merchant_type = selectedType;
        params.per_page = perPage.toString();

        router.get('/merchant', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleRefresh = () => {
        router.reload();
    };

    const openDeleteModal = (merchant: Merchant) => {
        setDeleteModal({ isOpen: true, merchant });
    };

    const closeDeleteModal = () => {
        setDeleteModal({ isOpen: false, merchant: null });
    };

    const handleDelete = () => {
        if (deleteModal.merchant) {
            router.delete(`/merchant/${deleteModal.merchant.id}`, {
                onSuccess: () => {
                    closeDeleteModal();
                },
                onError: (errors) => {
                    console.error('Delete error:', errors);
                }
            });
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Merchant Management" />
            
            <div className="space-y-6 p-6">
                {/* Header */}
                <div className="bg-white dark:bg-black rounded-lg shadow p-6">
                    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                        <div className="space-y-1">
                            <h1 className="text-2xl font-bold tracking-tight sm:text-3xl text-black dark:text-white">Merchant Management</h1>
                            <p className="text-sm text-black/70 dark:text-white/70 sm:text-base">
                                Manage your merchants and track their performance
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <button
                                onClick={handleRefresh}
                                className="px-3 py-2 text-sm border border-black/20 dark:border-white/20 text-black dark:text-white rounded-md hover:bg-black/5 dark:hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <RefreshCw className="h-4 w-4 mr-2 inline" />
                                Refresh
                            </button>
                            <a
                                href="/merchant/create"
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <Plus className="h-4 w-4 mr-2 inline" />
                                Add Merchant
                            </a>
                        </div>
                    </div>
                </div>

                {/* Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-white dark:bg-black rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-black/70 dark:text-white/70">Total Merchants</p>
                                <p className="text-2xl font-bold text-black dark:text-white">{stats.total_merchants}</p>
                            </div>
                            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-600/20 rounded-full flex items-center justify-center">
                                <Store className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                            </div>
                        </div>
                        <p className="text-xs text-black/50 dark:text-white/50 mt-1">{stats.active_merchants} active</p>
                    </div>

                    <div className="bg-white dark:bg-black rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-black/70 dark:text-white/70">Active Merchants</p>
                                <p className="text-2xl font-bold text-green-600 dark:text-green-400">{stats.active_merchants}</p>
                            </div>
                            <div className="w-8 h-8 bg-green-100 dark:bg-green-600/20 rounded-full flex items-center justify-center">
                                <Users className="w-4 h-4 text-green-600 dark:text-green-400" />
                            </div>
                        </div>
                        <p className="text-xs text-black/50 dark:text-white/50 mt-1">
                            {stats.total_merchants > 0 ? Math.round((stats.active_merchants / stats.total_merchants) * 100) : 0}% active rate
                        </p>
                    </div>

                    <div className="bg-white dark:bg-black rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-black/70 dark:text-white/70">Total Revenue</p>
                                <p className="text-2xl font-bold text-black dark:text-white">{formatCurrency(stats.total_revenue)}</p>
                            </div>
                            <div className="w-8 h-8 bg-purple-100 dark:bg-purple-600/20 rounded-full flex items-center justify-center">
                                <DollarSign className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                            </div>
                        </div>
                        <p className="text-xs text-black/50 dark:text-white/50 mt-1">From all merchants</p>
                    </div>

                    <div className="bg-white dark:bg-black rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-black/70 dark:text-white/70">Avg Revenue</p>
                                <p className="text-2xl font-bold text-black dark:text-white">
                                    {stats.active_merchants > 0 ? formatCurrency(stats.total_revenue / stats.active_merchants) : formatCurrency(0)}
                                </p>
                            </div>
                            <div className="w-8 h-8 bg-orange-100 dark:bg-orange-600/20 rounded-full flex items-center justify-center">
                                <TrendingUp className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                            </div>
                        </div>
                        <p className="text-xs text-black/50 dark:text-white/50 mt-1">Per active merchant</p>
                    </div>
                </div>

                {/* Filter Section */}
                <div className="bg-white dark:bg-black rounded-lg shadow p-6">
                    <h2 className="text-lg font-semibold text-black dark:text-white mb-4">Filter & Pencarian</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-black dark:text-white mb-1">Pencarian</label>
                            <input
                                type="text"
                                placeholder="Cari nama, kode, email, phone..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full px-3 py-2 border border-black/20 dark:border-white/20 bg-white dark:bg-black text-black dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-black dark:text-white mb-1">Status</label>
                            <select
                                value={selectedStatus}
                                onChange={(e) => setSelectedStatus(e.target.value)}
                                className="w-full px-3 py-2 border border-black/20 dark:border-white/20 bg-white dark:bg-black text-black dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">Semua Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-black dark:text-white mb-1">Tipe Merchant</label>
                            <select
                                value={selectedType}
                                onChange={(e) => setSelectedType(e.target.value)}
                                className="w-full px-3 py-2 border border-black/20 dark:border-white/20 bg-white dark:bg-black text-black dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">Semua Tipe</option>
                                <option value="online_store">Online Store</option>
                                <option value="restaurant">Restaurant</option>
                                <option value="retail">Retail</option>
                                <option value="service">Service</option>
                                <option value="digital_service">Digital Service</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-black dark:text-white mb-1">Per Halaman</label>
                            <select
                                value={currentPerPage}
                                onChange={(e) => changePerPage(parseInt(e.target.value))}
                                className="w-full px-3 py-2 border border-black/20 dark:border-white/20 bg-white dark:bg-black text-black dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value={10}>10</option>
                                <option value={25}>25</option>
                                <option value={50}>50</option>
                                <option value={100}>100</option>
                            </select>
                        </div>
                    </div>

                    <div className="flex items-center gap-2 mt-4">
                        <button
                            onClick={handleFilter}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <Search className="h-4 w-4 mr-2 inline" />
                            Terapkan Filter
                        </button>
                        <button
                            onClick={clearFilters}
                            className="px-4 py-2 border border-black/20 dark:border-white/20 text-black dark:text-white rounded-md hover:bg-black/5 dark:hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            Hapus Filter
                        </button>
                    </div>
                </div>

                {/* Merchants Table */}
                <div className="bg-white dark:bg-black rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-black/10 dark:border-white/10">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                            <div>
                                <h2 className="text-lg font-semibold text-black dark:text-white">Your Merchants</h2>
                                <p className="text-sm text-black/70 dark:text-white/70">
                                    A list of all your registered merchants ({merchants.total} merchants)
                                    {merchants.last_page > 1 && (
                                        <span className="ml-2 text-black/50 dark:text-white/50">
                                            - Halaman {merchants.current_page} dari {merchants.last_page}
                                        </span>
                                    )}
                                </p>
                            </div>
                            {merchants.total > 0 && (
                                <div className="text-sm text-black/50 dark:text-white/50">
                                    {merchants.per_page} per halaman
                                </div>
                            )}
                        </div>
                    </div>
                    <div className="overflow-x-auto">
                        {merchants.data.length === 0 ? (
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                                <div className="w-20 h-20 bg-black/10 dark:bg-white/10 rounded-full flex items-center justify-center mb-4">
                                    <Store className="w-10 h-10 text-black/40 dark:text-white/40" />
                                </div>
                                <h3 className="text-lg font-semibold text-black dark:text-white mb-2">No merchants found</h3>
                                <p className="text-sm text-black/70 dark:text-white/70 mb-6 max-w-sm">
                                    You haven't created any merchants yet. Create your first merchant to get started.
                                </p>
                                <a
                                    href="/merchant/create"
                                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <Plus className="h-4 w-4 mr-2 inline" />
                                    Create your first merchant
                                </a>
                            </div>
                        ) : (
                            <table className="min-w-full divide-y divide-black/10 dark:divide-white/10">
                                <thead className="bg-black/5 dark:bg-white/5">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-black/70 dark:text-white/70 uppercase tracking-wider">Merchant</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-black/70 dark:text-white/70 uppercase tracking-wider">Code</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-black/70 dark:text-white/70 uppercase tracking-wider">Type</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-black/70 dark:text-white/70 uppercase tracking-wider">Status</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-black/70 dark:text-white/70 uppercase tracking-wider">Transactions</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-black/70 dark:text-white/70 uppercase tracking-wider">Revenue</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-black/70 dark:text-white/70 uppercase tracking-wider">Created</th>
                                        <th className="px-6 py-3 text-right text-xs font-medium text-black/70 dark:text-white/70 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white dark:bg-black divide-y divide-black/10 dark:divide-white/10">
                                    {merchants.data.map((merchant) => (
                                        <tr key={merchant.id} className="hover:bg-black/5 dark:hover:bg-white/5">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div className="text-sm font-medium text-black dark:text-white">{merchant.merchant_name}</div>
                                                    {merchant.description && (
                                                        <div className="text-sm text-black/70 dark:text-white/70 truncate max-w-[200px]">
                                                            {merchant.description}
                                                        </div>
                                                    )}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <code className="text-sm bg-black/10 dark:bg-white/10 text-black dark:text-white px-2 py-1 rounded">
                                                    {merchant.merchant_code}
                                                </code>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-black/10 dark:bg-white/10 text-black dark:text-white">
                                                    {getMerchantTypeLabel(merchant.merchant_type)}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                {merchant.is_active ? (
                                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        Active
                                                    </span>
                                                ) : (
                                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        Inactive
                                                    </span>
                                                )}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-black dark:text-white">
                                                    {merchant.transactions_count} transactions
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-black dark:text-white">
                                                    {formatCurrency(merchant.total_transaction_amount)}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-black/70 dark:text-white/70">
                                                {formatDate(merchant.created_at)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <div className="flex items-center justify-end gap-2">
                                                    <a
                                                        href={`/merchant/${merchant.id}`}
                                                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 p-1 rounded hover:bg-blue-50 dark:hover:bg-blue-600/20"
                                                        title="View Details"
                                                    >
                                                        <Eye className="w-4 h-4" />
                                                    </a>
                                                    <a
                                                        href={`/merchant/${merchant.id}/edit`}
                                                        className="text-black/70 hover:text-black dark:text-white/70 dark:hover:text-white p-1 rounded hover:bg-black/5 dark:hover:bg-white/10"
                                                        title="Edit Merchant"
                                                    >
                                                        <Edit className="w-4 h-4" />
                                                    </a>
                                                    <button
                                                        onClick={() => openDeleteModal(merchant)}
                                                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-600/20"
                                                        title="Delete Merchant"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        )}
                    </div>
                </div>

                {/* Pagination */}
                {merchants.last_page > 1 && (
                    <div className="bg-white dark:bg-black rounded-lg shadow p-6">
                        <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
                            <div className="text-sm text-black dark:text-white">
                                Menampilkan {((merchants.current_page - 1) * merchants.per_page) + 1} sampai{' '}
                                {Math.min(merchants.current_page * merchants.per_page, merchants.total)} dari{' '}
                                {merchants.total} hasil
                            </div>

                            <div className="flex items-center gap-1">
                                {merchants.links.map((link, index) => {
                                    if (!link.url) {
                                        return (
                                            <span
                                                key={index}
                                                className="px-3 py-2 text-sm text-black/40 dark:text-white/40 cursor-not-allowed"
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        );
                                    }

                                    return (
                                        <button
                                            key={index}
                                            onClick={() => router.get(link.url!)}
                                            className={`px-3 py-2 text-sm rounded-md transition-colors ${
                                                link.active
                                                    ? 'bg-blue-600 text-white shadow-sm'
                                                    : 'text-black dark:text-white hover:bg-black/5 dark:hover:bg-white/10 border border-black/20 dark:border-white/20 hover:border-black/40 dark:hover:border-white/40'
                                            }`}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    );
                                })}
                            </div>
                        </div>

                        {/* Mobile-friendly page info */}
                        <div className="mt-4 pt-4 border-t border-black/10 dark:border-white/10 sm:hidden">
                            <div className="text-center text-sm text-black dark:text-white">
                                Halaman {merchants.current_page} dari {merchants.last_page}
                            </div>
                        </div>
                    </div>
                )}

                {/* Delete Confirmation Modal */}
                {deleteModal.isOpen && deleteModal.merchant && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                        <div className="bg-white dark:bg-black rounded-lg shadow-xl max-w-md w-full">
                            <div className="p-6">
                                <div className="flex items-center gap-4 mb-4">
                                    <div className="w-12 h-12 bg-red-100 dark:bg-red-600/20 rounded-full flex items-center justify-center">
                                        <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-semibold text-black dark:text-white">Delete Merchant</h3>
                                        <p className="text-sm text-black/70 dark:text-white/70">This action cannot be undone</p>
                                    </div>
                                </div>

                                <div className="mb-6">
                                    <p className="text-sm text-black dark:text-white">
                                        Are you sure you want to delete merchant{' '}
                                        <span className="font-semibold text-black dark:text-white">
                                            "{deleteModal.merchant.merchant_name}"
                                        </span>
                                        ?
                                    </p>
                                    <p className="text-sm text-black/70 dark:text-white/70 mt-2">
                                        This will permanently remove the merchant and all associated data.
                                    </p>
                                </div>

                                <div className="flex items-center gap-3 justify-end">
                                    <button
                                        onClick={closeDeleteModal}
                                        className="px-4 py-2 text-sm border border-black/20 dark:border-white/20 text-black dark:text-white rounded-md hover:bg-black/5 dark:hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handleDelete}
                                        className="px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                                    >
                                        Delete Merchant
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
