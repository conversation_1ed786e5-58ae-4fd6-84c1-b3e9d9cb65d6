import { Head, router, Link } from '@inertiajs/react';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Plus, Store, Eye, Edit, TrendingUp, Users, DollarSign, Search, Filter, RefreshCw, Trash2, Alert<PERSON>riangle } from 'lucide-react';

interface Merchant {
    id: number;
    merchant_code: string;
    merchant_name: string;
    merchant_type: string | null;
    description: string | null;
    contact_email: string | null;
    contact_phone: string | null;
    is_active: boolean;
    created_at: string;
    transactions_count: number;
    total_transaction_amount: number;
    successful_transactions: number;
    total_revenue: number;
}

interface MerchantsData {
    data: Merchant[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface Filters {
    status?: string;
    merchant_type?: string;
    search?: string;
    per_page?: number;
}

interface Stats {
    total_merchants: number;
    active_merchants: number;
    inactive_merchants: number;
    total_revenue: number;
}

interface MerchantIndexProps {
    merchants: MerchantsData;
    filters: Filters;
    stats: Stats;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Merchant',
        href: '/merchant',
    },
];

export default function MerchantClean({ merchants, filters, stats }: MerchantIndexProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
    const [selectedType, setSelectedType] = useState(filters.merchant_type || '');
    const [currentPerPage, setCurrentPerPage] = useState(filters.per_page || merchants.per_page || 25);
    const [deleteModal, setDeleteModal] = useState<{ isOpen: boolean; merchant: Merchant | null }>({
        isOpen: false,
        merchant: null
    });

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const getMerchantTypeLabel = (type: string | null) => {
        const types: Record<string, string> = {
            'online_store': 'Online Store',
            'restaurant': 'Restaurant',
            'service': 'Service',
            'retail': 'Retail',
            'digital_service': 'Digital Service',
        };
        return type ? types[type] || type : 'Not specified';
    };

    const handleFilter = () => {
        const params: Record<string, string> = {};
        
        if (searchTerm) params.search = searchTerm;
        if (selectedStatus) params.status = selectedStatus;
        if (selectedType) params.merchant_type = selectedType;
        if (currentPerPage !== 25) params.per_page = currentPerPage.toString();

        router.get('/merchant', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedStatus('');
        setSelectedType('');
        setCurrentPerPage(25);
        
        router.get('/merchant', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const changePerPage = (perPage: number) => {
        setCurrentPerPage(perPage);
        
        const params: Record<string, string> = {};
        
        if (searchTerm) params.search = searchTerm;
        if (selectedStatus) params.status = selectedStatus;
        if (selectedType) params.merchant_type = selectedType;
        params.per_page = perPage.toString();

        router.get('/merchant', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleRefresh = () => {
        router.reload();
    };

    const openDeleteModal = (merchant: Merchant) => {
        setDeleteModal({ isOpen: true, merchant });
    };

    const closeDeleteModal = () => {
        setDeleteModal({ isOpen: false, merchant: null });
    };

    const handleDelete = () => {
        if (deleteModal.merchant) {
            router.delete(`/merchant/${deleteModal.merchant.id}`, {
                onSuccess: () => {
                    closeDeleteModal();
                },
                onError: (errors) => {
                    console.error('Delete error:', errors);
                }
            });
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Merchant Management" />
            
            <div className="space-y-6 p-6">
                {/* Header */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                        <div className="space-y-1">
                            <h1 className="text-2xl font-bold tracking-tight sm:text-3xl text-gray-900 dark:text-white">Merchant Management</h1>
                            <p className="text-sm text-gray-600 dark:text-gray-300 sm:text-base">
                                Manage your merchants and track their performance
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <button 
                                onClick={handleRefresh}
                                className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <RefreshCw className="h-4 w-4 mr-2 inline" />
                                Refresh
                            </button>
                            <a 
                                href="/merchant/create"
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <Plus className="h-4 w-4 mr-2 inline" />
                                Add Merchant
                            </a>
                        </div>
                    </div>
                </div>

                {/* Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Total Merchants</p>
                                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.total_merchants}</p>
                            </div>
                            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                <Store className="w-4 h-4 text-blue-600 dark:text-blue-300" />
                            </div>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{stats.active_merchants} active</p>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Active Merchants</p>
                                <p className="text-2xl font-bold text-green-600 dark:text-green-400">{stats.active_merchants}</p>
                            </div>
                            <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                <Users className="w-4 h-4 text-green-600 dark:text-green-300" />
                            </div>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {stats.total_merchants > 0 ? Math.round((stats.active_merchants / stats.total_merchants) * 100) : 0}% active rate
                        </p>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Total Revenue</p>
                                <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatCurrency(stats.total_revenue)}</p>
                            </div>
                            <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                                <DollarSign className="w-4 h-4 text-purple-600 dark:text-purple-300" />
                            </div>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">From all merchants</p>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Avg Revenue</p>
                                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                                    {stats.active_merchants > 0 ? formatCurrency(stats.total_revenue / stats.active_merchants) : formatCurrency(0)}
                                </p>
                            </div>
                            <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                                <TrendingUp className="w-4 h-4 text-orange-600 dark:text-orange-300" />
                            </div>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Per active merchant</p>
                    </div>
                </div>

                {/* Filter Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Filter & Pencarian</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Pencarian</label>
                            <input
                                type="text"
                                placeholder="Cari nama, kode, email, phone..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                            <select
                                value={selectedStatus}
                                onChange={(e) => setSelectedStatus(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">Semua Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tipe Merchant</label>
                            <select
                                value={selectedType}
                                onChange={(e) => setSelectedType(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">Semua Tipe</option>
                                <option value="online_store">Online Store</option>
                                <option value="restaurant">Restaurant</option>
                                <option value="retail">Retail</option>
                                <option value="service">Service</option>
                                <option value="digital_service">Digital Service</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Per Halaman</label>
                            <select
                                value={currentPerPage}
                                onChange={(e) => changePerPage(parseInt(e.target.value))}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value={10}>10</option>
                                <option value={25}>25</option>
                                <option value={50}>50</option>
                                <option value={100}>100</option>
                            </select>
                        </div>
                    </div>

                    <div className="flex items-center gap-2 mt-4">
                        <button
                            onClick={handleFilter}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <Search className="h-4 w-4 mr-2 inline" />
                            Terapkan Filter
                        </button>
                        <button
                            onClick={clearFilters}
                            className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            Hapus Filter
                        </button>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
