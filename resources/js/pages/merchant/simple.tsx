import { Head, router, Link } from '@inertiajs/react';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Plus, Store, Eye, Edit, TrendingUp, Users, DollarSign, Search, Filter, RefreshCw, Trash2, Alert<PERSON>riangle } from 'lucide-react';

interface Merchant {
    id: number;
    merchant_code: string;
    merchant_name: string;
    merchant_type: string | null;
    description: string | null;
    contact_email: string | null;
    contact_phone: string | null;
    is_active: boolean;
    created_at: string;
    transactions_count: number;
    total_transaction_amount: number;
    successful_transactions: number;
    total_revenue: number;
}

interface MerchantsData {
    data: Merchant[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface Filters {
    status?: string;
    merchant_type?: string;
    search?: string;
    per_page?: number;
}

interface Stats {
    total_merchants: number;
    active_merchants: number;
    inactive_merchants: number;
    total_revenue: number;
}

interface MerchantIndexProps {
    merchants: MerchantsData;
    filters: Filters;
    stats: Stats;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Merchant',
        href: '/merchant',
    },
];

export default function MerchantSimple({ merchants, filters, stats }: MerchantIndexProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || '');
    const [selectedType, setSelectedType] = useState(filters.merchant_type || '');
    const [currentPerPage, setCurrentPerPage] = useState(filters.per_page || merchants.per_page || 25);
    const [deleteModal, setDeleteModal] = useState<{ isOpen: boolean; merchant: Merchant | null }>({
        isOpen: false,
        merchant: null
    });

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    const getMerchantTypeLabel = (type: string | null) => {
        const types: Record<string, string> = {
            'online_store': 'Online Store',
            'restaurant': 'Restaurant',
            'service': 'Service',
            'retail': 'Retail',
            'digital_service': 'Digital Service',
        };
        return type ? types[type] || type : 'Not specified';
    };

    const handleFilter = () => {
        const params: Record<string, string> = {};
        
        if (searchTerm) params.search = searchTerm;
        if (selectedStatus) params.status = selectedStatus;
        if (selectedType) params.merchant_type = selectedType;
        if (currentPerPage !== 25) params.per_page = currentPerPage.toString();

        router.get('/merchant', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedStatus('');
        setSelectedType('');
        setCurrentPerPage(25);
        
        router.get('/merchant', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const changePerPage = (perPage: number) => {
        setCurrentPerPage(perPage);
        
        const params: Record<string, string> = {};
        
        if (searchTerm) params.search = searchTerm;
        if (selectedStatus) params.status = selectedStatus;
        if (selectedType) params.merchant_type = selectedType;
        params.per_page = perPage.toString();

        router.get('/merchant', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleRefresh = () => {
        router.reload();
    };

    const openDeleteModal = (merchant: Merchant) => {
        setDeleteModal({ isOpen: true, merchant });
    };

    const closeDeleteModal = () => {
        setDeleteModal({ isOpen: false, merchant: null });
    };

    const handleDelete = () => {
        if (deleteModal.merchant) {
            router.delete(`/merchant/${deleteModal.merchant.id}`, {
                onSuccess: () => {
                    closeDeleteModal();
                },
                onError: (errors) => {
                    console.error('Delete error:', errors);
                }
            });
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Merchant Management" />

            <div className="space-y-6 p-6">
                {/* Header */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                        <div className="space-y-1">
                            <h1 className="text-2xl font-bold tracking-tight sm:text-3xl text-gray-900 dark:text-white">Merchant Management</h1>
                            <p className="text-sm text-gray-600 dark:text-gray-300 sm:text-base">
                                Manage your merchants and track their performance
                            </p>
                        </div>
                        <div className="flex items-center gap-2">
                            <button
                                onClick={handleRefresh}
                                className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <RefreshCw className="h-4 w-4 mr-2 inline" />
                                Refresh
                            </button>
                            <a
                                href="/merchant/create"
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <Plus className="h-4 w-4 mr-2 inline" />
                                Add Merchant
                            </a>
                        </div>
                    </div>
                </div>

                {/* Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Total Merchants</p>
                                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.total_merchants}</p>
                            </div>
                            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                <Store className="w-4 h-4 text-blue-600 dark:text-blue-300" />
                            </div>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{stats.active_merchants} active</p>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Active Merchants</p>
                                <p className="text-2xl font-bold text-green-600 dark:text-green-400">{stats.active_merchants}</p>
                            </div>
                            <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                <Users className="w-4 h-4 text-green-600 dark:text-green-300" />
                            </div>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {stats.total_merchants > 0 ? Math.round((stats.active_merchants / stats.total_merchants) * 100) : 0}% active rate
                        </p>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Total Revenue</p>
                                <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatCurrency(stats.total_revenue)}</p>
                            </div>
                            <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                                <DollarSign className="w-4 h-4 text-purple-600 dark:text-purple-300" />
                            </div>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">From all merchants</p>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Avg Revenue</p>
                                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                                    {stats.active_merchants > 0 ? formatCurrency(stats.total_revenue / stats.active_merchants) : formatCurrency(0)}
                                </p>
                            </div>
                            <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                                <TrendingUp className="w-4 h-4 text-orange-600 dark:text-orange-300" />
                            </div>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Per active merchant</p>
                    </div>
                </div>

                {/* Filter Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Filter & Pencarian</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Pencarian</label>
                            <input
                                type="text"
                                placeholder="Cari nama, kode, email, phone..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                            <select
                                value={selectedStatus}
                                onChange={(e) => setSelectedStatus(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">Semua Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                            <div>
                                <Label htmlFor="type">Tipe Merchant</Label>
                                <Select value={selectedType} onValueChange={setSelectedType}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Semua Tipe" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">Semua Tipe</SelectItem>
                                        <SelectItem value="online_store">Online Store</SelectItem>
                                        <SelectItem value="restaurant">Restaurant</SelectItem>
                                        <SelectItem value="retail">Retail</SelectItem>
                                        <SelectItem value="service">Service</SelectItem>
                                        <SelectItem value="digital_service">Digital Service</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label htmlFor="per-page">Per Halaman</Label>
                                <Select value={currentPerPage.toString()} onValueChange={(value) => changePerPage(parseInt(value))}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="10">10</SelectItem>
                                        <SelectItem value="25">25</SelectItem>
                                        <SelectItem value="50">50</SelectItem>
                                        <SelectItem value="100">100</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                        <div className="flex items-center gap-2 mt-4">
                            <Button onClick={handleFilter}>
                                <Search className="h-4 w-4 mr-2" />
                                Terapkan Filter
                            </Button>
                            <Button variant="outline" onClick={clearFilters}>
                                Hapus Filter
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Merchants Table */}
                <Card>
                    <CardHeader>
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                            <div>
                                <CardTitle>Your Merchants</CardTitle>
                                <CardDescription>
                                    A list of all your registered merchants ({merchants.total} merchants)
                                    {merchants.last_page > 1 && (
                                        <span className="ml-2 text-muted-foreground">
                                            - Halaman {merchants.current_page} dari {merchants.last_page}
                                        </span>
                                    )}
                                </CardDescription>
                            </div>
                            {merchants.total > 0 && (
                                <Badge variant="secondary" className="text-sm">
                                    {merchants.per_page} per halaman
                                </Badge>
                            )}
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Merchant</TableHead>
                                        <TableHead>Code</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Transactions</TableHead>
                                        <TableHead>Revenue</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {merchants.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={8} className="h-24 text-center">
                                                <div className="flex flex-col items-center gap-2">
                                                    <Store className="h-8 w-8 text-muted-foreground" />
                                                    <p className="text-muted-foreground">No merchants found</p>
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href="/merchant/create">
                                                            <Plus className="h-4 w-4 mr-2" />
                                                            Create your first merchant
                                                        </Link>
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        merchants.data.map((merchant) => (
                                            <TableRow key={merchant.id}>
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium">{merchant.merchant_name}</div>
                                                        {merchant.description && (
                                                            <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                                                                {merchant.description}
                                                            </div>
                                                        )}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <code className="text-sm bg-muted px-2 py-1 rounded">
                                                        {merchant.merchant_code}
                                                    </code>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant="outline">
                                                        {getMerchantTypeLabel(merchant.merchant_type)}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    {merchant.is_active ? (
                                                        <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
                                                            Active
                                                        </Badge>
                                                    ) : (
                                                        <Badge variant="secondary" className="bg-red-100 text-red-800 hover:bg-red-100">
                                                            Inactive
                                                        </Badge>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="text-sm">
                                                        {merchant.transactions_count} transactions
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="text-sm font-medium">
                                                        {formatCurrency(merchant.total_transaction_amount)}
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-muted-foreground">
                                                    {formatDate(merchant.created_at)}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex items-center justify-end gap-2">
                                                        <Button variant="ghost" size="sm" asChild>
                                                            <Link href={`/merchant/${merchant.id}`}>
                                                                <Eye className="w-4 h-4" />
                                                            </Link>
                                                        </Button>
                                                        <Button variant="ghost" size="sm" asChild>
                                                            <Link href={`/merchant/${merchant.id}/edit`}>
                                                                <Edit className="w-4 h-4" />
                                                            </Link>
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => openDeleteModal(merchant)}
                                                            className="text-red-600 hover:text-red-900 hover:bg-red-50"
                                                        >
                                                            <Trash2 className="w-4 h-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>

                {/* Pagination */}
                {merchants.last_page > 1 && (
                    <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">
                            Showing {((merchants.current_page - 1) * merchants.per_page) + 1} to{' '}
                            {Math.min(merchants.current_page * merchants.per_page, merchants.total)} of{' '}
                            {merchants.total} results
                        </div>
                        <div className="flex items-center gap-2">
                            {merchants.links.map((link, index) => {
                                if (!link.url) {
                                    return (
                                        <Button
                                            key={index}
                                            variant="outline"
                                            size="sm"
                                            disabled
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    );
                                }

                                return (
                                    <Button
                                        key={index}
                                        variant={link.active ? "default" : "outline"}
                                        size="sm"
                                        asChild
                                    >
                                        <Link
                                            href={link.url}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    </Button>
                                );
                            })}
                        </div>
                    </div>
                )}

                {/* Delete Confirmation Modal */}
                {deleteModal.isOpen && deleteModal.merchant && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                        <Card className="max-w-md w-full">
                            <CardContent className="p-6">
                                <div className="flex items-center gap-4 mb-4">
                                    <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                                        <AlertTriangle className="w-6 h-6 text-red-600" />
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-semibold">Delete Merchant</h3>
                                        <p className="text-sm text-muted-foreground">This action cannot be undone</p>
                                    </div>
                                </div>

                                <div className="mb-6">
                                    <p className="text-sm">
                                        Are you sure you want to delete merchant{' '}
                                        <span className="font-semibold">
                                            "{deleteModal.merchant.merchant_name}"
                                        </span>
                                        ?
                                    </p>
                                    <p className="text-sm text-muted-foreground mt-2">
                                        This will permanently remove the merchant and all associated data.
                                    </p>
                                </div>

                                <div className="flex items-center gap-3 justify-end">
                                    <Button variant="outline" onClick={closeDeleteModal}>
                                        Cancel
                                    </Button>
                                    <Button variant="destructive" onClick={handleDelete}>
                                        Delete Merchant
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
