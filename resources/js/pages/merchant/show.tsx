import { Head } from '@inertiajs/react';
import { ArrowLeft, Edit, Eye, Trash2, Globe, Mail, Phone, MapPin, Calendar, DollarSign, TrendingUp, Users } from 'lucide-react';

import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import AuthenticatedLayout from '@/layouts/authenticated-layout';

interface Merchant {
    id: number;
    merchant_code: string;
    merchant_name: string;
    merchant_type: string;
    description?: string;
    website_url?: string;
    contact_email?: string;
    contact_phone?: string;
    address?: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

interface MerchantStats {
    total_transactions: number;
    total_revenue: number;
    success_rate: number;
    avg_transaction: number;
}

interface ShowMerchantProps {
    merchant: Merchant;
    stats: MerchantStats;
}

export default function ShowMerchant({ merchant, stats }: ShowMerchantProps) {
    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const getMerchantTypeLabel = (type: string) => {
        const types: Record<string, string> = {
            'online_store': 'Online Store',
            'restaurant': 'Restaurant',
            'retail': 'Retail',
            'service': 'Service',
            'digital_service': 'Digital Service',
            'other': 'Other',
        };
        return types[type] || type;
    };

    return (
        <AuthenticatedLayout>
            <Head title={`${merchant.merchant_name} - Merchant Details`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <TextLink href={route('merchant.index')} className="flex items-center text-muted-foreground hover:text-foreground">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Merchants
                        </TextLink>
                        <div>
                            <h1 className="text-2xl font-bold">{merchant.merchant_name}</h1>
                            <p className="text-muted-foreground">Merchant Details</p>
                        </div>
                    </div>
                    <div className="flex items-center space-x-2">
                        <TextLink href={route('merchant.edit', merchant.id)}>
                            <Button variant="outline">
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Merchant
                            </Button>
                        </TextLink>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-2">
                                <Users className="h-4 w-4 text-muted-foreground" />
                                <div className="space-y-1">
                                    <p className="text-sm font-medium text-muted-foreground">Total Transactions</p>
                                    <p className="text-2xl font-bold">{stats.total_transactions.toLocaleString()}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-2">
                                <DollarSign className="h-4 w-4 text-muted-foreground" />
                                <div className="space-y-1">
                                    <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                                    <p className="text-2xl font-bold">{formatCurrency(stats.total_revenue)}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-2">
                                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                                <div className="space-y-1">
                                    <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                                    <p className="text-2xl font-bold">{stats.success_rate.toFixed(1)}%</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-2">
                                <DollarSign className="h-4 w-4 text-muted-foreground" />
                                <div className="space-y-1">
                                    <p className="text-sm font-medium text-muted-foreground">Avg Transaction</p>
                                    <p className="text-2xl font-bold">{formatCurrency(stats.avg_transaction)}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Merchant Information */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Basic Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Basic Information</CardTitle>
                            <CardDescription>Merchant details and configuration</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-muted-foreground">Merchant Code</span>
                                <span className="font-mono font-medium">{merchant.merchant_code}</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-muted-foreground">Type</span>
                                <Badge variant="secondary">{getMerchantTypeLabel(merchant.merchant_type)}</Badge>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-muted-foreground">Status</span>
                                <Badge variant={merchant.is_active ? "default" : "destructive"}>
                                    {merchant.is_active ? 'Active' : 'Inactive'}
                                </Badge>
                            </div>

                            {merchant.description && (
                                <>
                                    <Separator />
                                    <div>
                                        <span className="text-sm font-medium text-muted-foreground">Description</span>
                                        <p className="mt-1 text-sm">{merchant.description}</p>
                                    </div>
                                </>
                            )}

                            <Separator />
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-muted-foreground">Created</span>
                                <span className="text-sm">{formatDate(merchant.created_at)}</span>
                            </div>

                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-muted-foreground">Last Updated</span>
                                <span className="text-sm">{formatDate(merchant.updated_at)}</span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Contact Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Contact Information</CardTitle>
                            <CardDescription>How to reach this merchant</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {merchant.contact_email && (
                                <div className="flex items-center space-x-3">
                                    <Mail className="h-4 w-4 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm font-medium">Email</p>
                                        <a href={`mailto:${merchant.contact_email}`} className="text-sm text-blue-600 hover:underline">
                                            {merchant.contact_email}
                                        </a>
                                    </div>
                                </div>
                            )}

                            {merchant.contact_phone && (
                                <div className="flex items-center space-x-3">
                                    <Phone className="h-4 w-4 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm font-medium">Phone</p>
                                        <a href={`tel:${merchant.contact_phone}`} className="text-sm text-blue-600 hover:underline">
                                            {merchant.contact_phone}
                                        </a>
                                    </div>
                                </div>
                            )}

                            {merchant.website_url && (
                                <div className="flex items-center space-x-3">
                                    <Globe className="h-4 w-4 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm font-medium">Website</p>
                                        <a href={merchant.website_url} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-600 hover:underline">
                                            {merchant.website_url}
                                        </a>
                                    </div>
                                </div>
                            )}

                            {merchant.address && (
                                <div className="flex items-start space-x-3">
                                    <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                                    <div>
                                        <p className="text-sm font-medium">Address</p>
                                        <p className="text-sm text-muted-foreground">{merchant.address}</p>
                                    </div>
                                </div>
                            )}

                            {!merchant.contact_email && !merchant.contact_phone && !merchant.website_url && !merchant.address && (
                                <p className="text-sm text-muted-foreground">No contact information available</p>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
