import { Head, useForm } from '@inertiajs/react';
import { ArrowLeft, Save } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import AuthenticatedLayout from '@/layouts/authenticated-layout';

interface Merchant {
    id: number;
    merchant_code: string;
    merchant_name: string;
    merchant_type: string;
    description?: string;
    website_url?: string;
    contact_email?: string;
    contact_phone?: string;
    address?: string;
    is_active: boolean;
}

interface EditMerchantProps {
    merchant: Merchant;
}

export default function EditMerchant({ merchant }: EditMerchantProps) {
    const { data, setData, patch, processing, errors } = useForm({
        merchant_name: merchant.merchant_name,
        merchant_type: merchant.merchant_type,
        description: merchant.description || '',
        website_url: merchant.website_url || '',
        contact_email: merchant.contact_email || '',
        contact_phone: merchant.contact_phone || '',
        address: merchant.address || '',
        is_active: merchant.is_active,
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        patch(route('merchant.update', merchant.id));
    };

    const merchantTypes = [
        { value: 'online_store', label: 'Online Store' },
        { value: 'restaurant', label: 'Restaurant' },
        { value: 'retail', label: 'Retail' },
        { value: 'service', label: 'Service' },
        { value: 'digital_service', label: 'Digital Service' },
        { value: 'other', label: 'Other' },
    ];

    return (
        <AuthenticatedLayout>
            <Head title={`Edit Merchant - ${merchant.merchant_name}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <TextLink href={route('merchant.index')} className="flex items-center text-muted-foreground hover:text-foreground">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Merchants
                        </TextLink>
                        <div>
                            <h1 className="text-2xl font-bold">Edit Merchant</h1>
                            <p className="text-muted-foreground">Update merchant information</p>
                        </div>
                    </div>
                </div>

                {/* Edit Form */}
                <Card>
                    <CardHeader>
                        <CardTitle>Merchant Information</CardTitle>
                        <CardDescription>
                            Update your merchant details below. Code: <span className="font-mono font-medium">{merchant.merchant_code}</span>
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={submit} className="space-y-6">
                            {/* Basic Information */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium">Basic Information</h3>
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="merchant_name">Merchant Name *</Label>
                                        <Input
                                            id="merchant_name"
                                            type="text"
                                            value={data.merchant_name}
                                            onChange={(e) => setData('merchant_name', e.target.value)}
                                            placeholder="Enter merchant name"
                                            required
                                        />
                                        <InputError message={errors.merchant_name} />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="merchant_type">Merchant Type *</Label>
                                        <Select value={data.merchant_type} onValueChange={(value) => setData('merchant_type', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select merchant type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {merchantTypes.map((type) => (
                                                    <SelectItem key={type.value} value={type.value}>
                                                        {type.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <InputError message={errors.merchant_type} />
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Describe your merchant business"
                                        rows={3}
                                    />
                                    <InputError message={errors.description} />
                                </div>
                            </div>

                            {/* Contact Information */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium">Contact Information</h3>
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="contact_email">Contact Email</Label>
                                        <Input
                                            id="contact_email"
                                            type="email"
                                            value={data.contact_email}
                                            onChange={(e) => setData('contact_email', e.target.value)}
                                            placeholder="<EMAIL>"
                                        />
                                        <InputError message={errors.contact_email} />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="contact_phone">Contact Phone</Label>
                                        <Input
                                            id="contact_phone"
                                            type="tel"
                                            value={data.contact_phone}
                                            onChange={(e) => setData('contact_phone', e.target.value)}
                                            placeholder="+6281234567890"
                                        />
                                        <InputError message={errors.contact_phone} />
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="website_url">Website URL</Label>
                                    <Input
                                        id="website_url"
                                        type="url"
                                        value={data.website_url}
                                        onChange={(e) => setData('website_url', e.target.value)}
                                        placeholder="https://merchant.com"
                                    />
                                    <InputError message={errors.website_url} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="address">Address</Label>
                                    <Textarea
                                        id="address"
                                        value={data.address}
                                        onChange={(e) => setData('address', e.target.value)}
                                        placeholder="Enter merchant address"
                                        rows={2}
                                    />
                                    <InputError message={errors.address} />
                                </div>
                            </div>

                            {/* Status */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium">Status</h3>
                                
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="is_active"
                                        checked={data.is_active}
                                        onCheckedChange={(checked) => setData('is_active', checked)}
                                    />
                                    <Label htmlFor="is_active">Active Merchant</Label>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                    Inactive merchants cannot process transactions
                                </p>
                            </div>

                            {/* Submit Button */}
                            <div className="flex items-center justify-end space-x-4 pt-6 border-t">
                                <TextLink href={route('merchant.show', merchant.id)} className="text-muted-foreground hover:text-foreground">
                                    Cancel
                                </TextLink>
                                <Button type="submit" disabled={processing}>
                                    <Save className="h-4 w-4 mr-2" />
                                    {processing ? 'Updating...' : 'Update Merchant'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AuthenticatedLayout>
    );
}
