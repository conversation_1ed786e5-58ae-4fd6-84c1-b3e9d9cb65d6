import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';
import { ArrowLeft, Store, Save } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import InputError from '@/components/input-error';
import AppLayout from '@/layouts/app-layout';

type MerchantForm = {
    merchant_name: string;
    merchant_type: string;
    description: string;
    website_url: string;
    contact_email: string;
    contact_phone: string;
    address: string;
};

const merchantTypes = [
    { value: 'online_store', label: 'Online Store' },
    { value: 'restaurant', label: 'Restaurant' },
    { value: 'service', label: 'Service' },
    { value: 'retail', label: 'Retail' },
    { value: 'digital_service', label: 'Digital Service' },
];

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Merchant',
        href: '/merchant',
    },
    {
        title: 'Create Merchant',
        href: '#',
    },
];

export default function CreateMerchant() {
    const { data, setData, post, errors, processing } = useForm<Required<MerchantForm>>({
        merchant_name: '',
        merchant_type: '',
        description: '',
        website_url: '',
        contact_email: '',
        contact_phone: '',
        address: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('merchant.store'));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Merchant" />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
                    <Button variant="outline" size="sm" asChild className="w-fit">
                        <Link href={route('merchant.index')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            <span className="hidden sm:inline">Back to Merchants</span>
                            <span className="sm:hidden">Back</span>
                        </Link>
                    </Button>
                    <div className="space-y-1">
                        <h1 className="text-2xl font-bold tracking-tight sm:text-3xl">Create New Merchant</h1>
                        <p className="text-sm text-muted-foreground sm:text-base">
                            Add a new merchant to start accepting payments
                        </p>
                    </div>
                </div>

                {/* Form */}
                <form onSubmit={submit} className="space-y-6">
                    {/* Basic Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Store className="h-5 w-5" />
                                Basic Information
                            </CardTitle>
                            <CardDescription>
                                Enter the basic details for your merchant
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="grid gap-2">
                                    <Label htmlFor="merchant_name">Merchant Name *</Label>
                                    <Input
                                        id="merchant_name"
                                        value={data.merchant_name}
                                        onChange={(e) => setData('merchant_name', e.target.value)}
                                        required
                                        placeholder="e.g., My Online Store"
                                    />
                                    <InputError message={errors.merchant_name} />
                                </div>

                                <div className="grid gap-2">
                                    <Label htmlFor="merchant_type">Merchant Type</Label>
                                    <Select value={data.merchant_type} onValueChange={(value) => setData('merchant_type', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select merchant type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {merchantTypes.map((type) => (
                                                <SelectItem key={type.value} value={type.value}>
                                                    {type.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.merchant_type} />
                                </div>
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Describe your business..."
                                    rows={3}
                                />
                                <InputError message={errors.description} />
                            </div>
                        </CardContent>
                    </Card>

                    {/* Contact Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Contact Information</CardTitle>
                            <CardDescription>
                                Optional contact details for your merchant
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="grid gap-2">
                                    <Label htmlFor="contact_email">Contact Email</Label>
                                    <Input
                                        id="contact_email"
                                        type="email"
                                        value={data.contact_email}
                                        onChange={(e) => setData('contact_email', e.target.value)}
                                        placeholder="<EMAIL>"
                                    />
                                    <InputError message={errors.contact_email} />
                                </div>

                                <div className="grid gap-2">
                                    <Label htmlFor="contact_phone">Contact Phone</Label>
                                    <Input
                                        id="contact_phone"
                                        type="tel"
                                        value={data.contact_phone}
                                        onChange={(e) => setData('contact_phone', e.target.value)}
                                        placeholder="+62812345678"
                                    />
                                    <InputError message={errors.contact_phone} />
                                </div>
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="website_url">Website URL</Label>
                                <Input
                                    id="website_url"
                                    type="url"
                                    value={data.website_url}
                                    onChange={(e) => setData('website_url', e.target.value)}
                                    placeholder="https://www.merchant.com"
                                />
                                <InputError message={errors.website_url} />
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="address">Address</Label>
                                <Textarea
                                    id="address"
                                    value={data.address}
                                    onChange={(e) => setData('address', e.target.value)}
                                    placeholder="Business address..."
                                    rows={3}
                                />
                                <InputError message={errors.address} />
                            </div>
                        </CardContent>
                    </Card>

                    {/* Submit Button */}
                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                <div className="space-y-1">
                                    <h3 className="font-medium">Create Merchant</h3>
                                    <p className="text-sm text-muted-foreground">
                                        Your merchant will be created and activated immediately
                                    </p>
                                </div>
                                <Button disabled={processing} className="w-full sm:w-auto sm:min-w-[140px]">
                                    {processing ? (
                                        <>
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                            <span className="hidden sm:inline">Creating...</span>
                                            <span className="sm:hidden">Creating...</span>
                                        </>
                                    ) : (
                                        <>
                                            <Save className="h-4 w-4 mr-2" />
                                            <span className="hidden sm:inline">Create Merchant</span>
                                            <span className="sm:hidden">Create</span>
                                        </>
                                    )}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </form>
            </div>
        </AppLayout>
    );
}
