import { type BreadcrumbItem, type SharedData } from '@/types';
import { Transition } from '@headlessui/react';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { FormEventHandler } from 'react';

import DeleteUser from '@/components/delete-user';
import HeadingSmall from '@/components/heading-small';
import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import SettingsLayout from '@/layouts/settings/layout';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Profile settings',
        href: '/settings/profile',
    },
];

type ProfileForm = {
    name: string;
    email: string;
    phone: string;
    bank_name: string;
    account_number: string;
    account_holder_name: string;
    withdrawal_limit: string;
};

export default function Profile({ mustVerifyEmail, status }: { mustVerifyEmail: boolean; status?: string }) {
    const { auth } = usePage<SharedData>().props;

    const { data, setData, patch, errors, processing, recentlySuccessful } = useForm<Required<ProfileForm>>({
        name: auth.user.name,
        email: auth.user.email,
        phone: auth.user.phone || '+62',
        bank_name: auth.user.bank_name || '',
        account_number: auth.user.account_number || '',
        account_holder_name: auth.user.account_holder_name || '',
        withdrawal_limit: auth.user.withdrawal_limit ? auth.user.withdrawal_limit.toString() : '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        patch(route('profile.update'), {
            preserveScroll: true,
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Profile settings" />

            <SettingsLayout>
                <div className="space-y-6">
                    <HeadingSmall title="Profile information" description="Update your name, email address, and phone number" />

                    <form onSubmit={submit} className="space-y-6">
                        <div className="grid gap-2">
                            <Label htmlFor="name">Name</Label>

                            <Input
                                id="name"
                                className="mt-1 block w-full"
                                value={data.name}
                                onChange={(e) => setData('name', e.target.value)}
                                required
                                autoComplete="name"
                                placeholder="Full name"
                            />

                            <InputError className="mt-2" message={errors.name} />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="email">Email address</Label>

                            <Input
                                id="email"
                                type="email"
                                className="mt-1 block w-full"
                                value={data.email}
                                onChange={(e) => setData('email', e.target.value)}
                                required
                                autoComplete="username"
                                placeholder="Email address"
                            />

                            <InputError className="mt-2" message={errors.email} />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="phone">Phone number</Label>

                            <Input
                                id="phone"
                                type="tel"
                                className="mt-1 block w-full"
                                value={data.phone}
                                onChange={(e) => {
                                    let value = e.target.value;
                                    // Ensure it always starts with +62
                                    if (!value.startsWith('+62')) {
                                        value = '+62' + value.replace(/^\+?62?/, '');
                                    }
                                    // Remove any non-digit characters except +
                                    value = value.replace(/[^\d+]/g, '');
                                    // Limit length to +62 + 13 digits max
                                    if (value.length > 16) {
                                        value = value.substring(0, 16);
                                    }
                                    setData('phone', value);
                                }}
                                required
                                autoComplete="tel"
                                placeholder="+62812345678"
                            />

                            <InputError className="mt-2" message={errors.phone} />
                        </div>

                        {mustVerifyEmail && auth.user.email_verified_at === null && (
                            <div>
                                <p className="-mt-4 text-sm text-muted-foreground">
                                    Your email address is unverified.{' '}
                                    <Link
                                        href={route('verification.send')}
                                        method="post"
                                        as="button"
                                        className="text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"
                                    >
                                        Click here to resend the verification email.
                                    </Link>
                                </p>

                                {status === 'verification-link-sent' && (
                                    <div className="mt-2 text-sm font-medium text-green-600">
                                        A new verification link has been sent to your email address.
                                    </div>
                                )}
                            </div>
                        )}

                        <div className="flex items-center gap-4">
                            <Button disabled={processing}>Save</Button>

                            <Transition
                                show={recentlySuccessful}
                                enter="transition ease-in-out"
                                enterFrom="opacity-0"
                                leave="transition ease-in-out"
                                leaveTo="opacity-0"
                            >
                                <p className="text-sm text-neutral-600">Saved</p>
                            </Transition>
                        </div>
                    </form>
                </div>

                <div className="space-y-6">
                    <HeadingSmall title="Bank Account Details" description="Edit your account details" />

                    <form onSubmit={submit} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="grid gap-2">
                                <Label htmlFor="bank_name">Bank Name</Label>
                                <Input
                                    id="bank_name"
                                    className="mt-1 block w-full"
                                    value={data.bank_name}
                                    onChange={(e) => setData('bank_name', e.target.value)}
                                    autoComplete="off"
                                    placeholder="e.g., Bank BCA, Bank Mandiri"
                                />
                                <InputError className="mt-2" message={errors.bank_name} />
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="account_number">Account Number</Label>
                                <Input
                                    id="account_number"
                                    className="mt-1 block w-full"
                                    value={data.account_number}
                                    onChange={(e) => {
                                        // Only allow numbers
                                        const value = e.target.value.replace(/[^\d]/g, '');
                                        setData('account_number', value);
                                    }}
                                    autoComplete="off"
                                    placeholder="e.g., **********"
                                />
                                <InputError className="mt-2" message={errors.account_number} />
                            </div>
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="account_holder_name">Account Holder Name</Label>
                            <Input
                                id="account_holder_name"
                                className="mt-1 block w-full"
                                value={data.account_holder_name}
                                onChange={(e) => setData('account_holder_name', e.target.value)}
                                autoComplete="off"
                                placeholder="Full name as registered in bank"
                            />
                            <InputError className="mt-2" message={errors.account_holder_name} />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="withdrawal_limit">Withdrawal Limit (IDR)</Label>
                            <Input
                                id="withdrawal_limit"
                                className="mt-1 block w-full"
                                value={data.withdrawal_limit}
                                onChange={(e) => {
                                    // Only allow numbers and format as currency
                                    const value = e.target.value.replace(/[^\d]/g, '');
                                    setData('withdrawal_limit', value);
                                }}
                                autoComplete="off"
                                placeholder="e.g., 5000000"
                            />
                            <InputError className="mt-2" message={errors.withdrawal_limit} />
                            <p className="text-sm text-muted-foreground">
                                Maximum amount you can withdraw per transaction
                            </p>
                        </div>

                        <div className="flex items-center gap-4">
                            <Button disabled={processing}>Save Bank Details</Button>

                            <Transition
                                show={recentlySuccessful}
                                enter="transition ease-in-out"
                                enterFrom="opacity-0"
                                leave="transition ease-in-out"
                                leaveTo="opacity-0"
                            >
                                <p className="text-sm text-neutral-600">Saved</p>
                            </Transition>
                        </div>
                    </form>
                </div>

                <DeleteUser />
            </SettingsLayout>
        </AppLayout>
    );
}
