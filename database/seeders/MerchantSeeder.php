<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Merchant;
use App\Models\User;

class MerchantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user (assuming there's at least one user)
        $user = User::first();
        
        if (!$user) {
            $this->command->info('No users found. Creating a test user first.');
            $user = User::create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'user',
                'email_verified_at' => now(),
            ]);
        }

        // Create sample merchants
        $merchants = [
            [
                'merchant_name' => 'Toko Online Sejahtera',
                'merchant_type' => 'online_store',
                'description' => 'Toko online yang menjual berbagai kebutuhan sehari-hari',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+6281234567890',
                'website_url' => 'https://tokosejahtera.com',
                'address' => 'Jl. Sudirman No. 123, Jakarta Pusat',
                'is_active' => true,
            ],
            [
                'merchant_name' => 'Warung Kopi Nusantara',
                'merchant_type' => 'restaurant',
                'description' => 'Warung kopi dengan cita rasa khas Indonesia',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+6281234567891',
                'website_url' => 'https://kopinusantara.com',
                'address' => 'Jl. Thamrin No. 456, Jakarta Pusat',
                'is_active' => true,
            ],
            [
                'merchant_name' => 'Fashion Store Modern',
                'merchant_type' => 'retail',
                'description' => 'Toko fashion dengan koleksi terkini',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+6281234567892',
                'website_url' => 'https://fashionmodern.com',
                'address' => 'Jl. Gatot Subroto No. 789, Jakarta Selatan',
                'is_active' => true,
            ],
            [
                'merchant_name' => 'Digital Service Pro',
                'merchant_type' => 'digital_service',
                'description' => 'Layanan digital profesional untuk bisnis',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+6281234567893',
                'website_url' => 'https://digitalservicepro.com',
                'address' => 'Jl. Kuningan No. 321, Jakarta Selatan',
                'is_active' => true,
            ],
            [
                'merchant_name' => 'Bengkel Motor Jaya',
                'merchant_type' => 'service',
                'description' => 'Bengkel motor terpercaya dengan teknisi berpengalaman',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+6281234567894',
                'address' => 'Jl. Raya Bekasi No. 654, Bekasi',
                'is_active' => true,
            ],
        ];

        foreach ($merchants as $merchantData) {
            Merchant::create(array_merge($merchantData, [
                'user_id' => $user->id,
                'merchant_code' => 'MCH' . str_pad(Merchant::count() + 1, 3, '0', STR_PAD_LEFT),
            ]));
        }

        $this->command->info('5 sample merchants created successfully!');
    }
}
