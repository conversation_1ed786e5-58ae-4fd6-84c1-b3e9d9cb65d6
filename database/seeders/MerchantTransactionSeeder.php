<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Merchant;
use App\Models\Transaction;
use Illuminate\Database\Seeder;

class MerchantTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing users with role 'user'
        $users = User::where('role', 'user')->get();

        if ($users->isEmpty()) {
            // Create some test users if none exist
            $users = User::factory()->count(3)->create(['role' => 'user']);
        }

        foreach ($users as $user) {
            // Create 2-3 merchants per user
            $merchants = Merchant::factory()
                ->count(rand(2, 3))
                ->forUser($user)
                ->create();

            foreach ($merchants as $merchant) {
                // Create transactions for each merchant
                Transaction::factory()
                    ->count(rand(10, 25))
                    ->forMerchant($merchant)
                    ->create();
            }
        }

        $this->command->info('Sample merchants and transactions created successfully!');
    }
}
