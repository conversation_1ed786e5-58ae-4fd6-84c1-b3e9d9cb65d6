<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Transaction;
use App\Models\Merchant;
use App\Models\User;
use Carbon\Carbon;

class TransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all merchants
        $merchants = Merchant::all();

        if ($merchants->isEmpty()) {
            $this->command->info('No merchants found. Creating sample merchants first.');
            $this->call(MerchantSeeder::class);
            $merchants = Merchant::all();
        }

        // Clear existing transactions
        Transaction::truncate();
        $this->command->info('Cleared existing transactions.');

        $statuses = ['pending', 'success', 'failed'];
        $paymentMethods = ['bank_transfer', 'e_wallet', 'credit_card', 'qris'];
        
        // Sample customer data
        $customers = [
            ['name' => 'Ahmad Rizki', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Siti Nurhaliza', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Budi <PERSON>o', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Dewi Sartika', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Eko Prasetyo', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Fitri Handayani', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Gunawan Wijaya', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Hani Kusuma', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Indra Permana', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Joko Widodo', 'email' => '<EMAIL>', 'phone' => '+6281234567899'],
        ];

        // Create 50 sample transactions
        for ($i = 1; $i <= 50; $i++) {
            $merchant = $merchants->random();
            $customer = $customers[array_rand($customers)];
            $status = $statuses[array_rand($statuses)];
            $paymentMethod = $paymentMethods[array_rand($paymentMethods)];
            
            // Random amount between 10,000 and 5,000,000
            $amount = rand(10000, 5000000);
            $fee = $amount * 0.025; // 2.5% fee
            
            // Random date within last 3 months
            $createdAt = Carbon::now()->subDays(rand(0, 90));
            
            Transaction::create([
                'merchant_id' => $merchant->id,
                'transaction_reference' => 'TXN' . str_pad($i, 6, '0', STR_PAD_LEFT),
                'amount' => $amount,
                'fee' => $fee,
                'status' => $status,
                'payment_method' => $paymentMethod,
                'customer_name' => $customer['name'],
                'customer_email' => $customer['email'],
                'customer_phone' => $customer['phone'],
                'description' => $this->generateDescription($merchant->merchant_name, $paymentMethod),
                'metadata' => json_encode([
                    'ip_address' => $this->generateRandomIP(),
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'payment_gateway' => $this->getPaymentGateway($paymentMethod),
                ]),
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);
        }

        $this->command->info('50 sample transactions created successfully!');
    }

    private function generateDescription($merchantName, $paymentMethod): string
    {
        $descriptions = [
            'bank_transfer' => "Payment for order from {$merchantName} via Bank Transfer",
            'e_wallet' => "Payment for order from {$merchantName} via E-Wallet",
            'credit_card' => "Payment for order from {$merchantName} via Credit Card",
            'qris' => "Payment for order from {$merchantName} via QRIS",
        ];

        return $descriptions[$paymentMethod] ?? "Payment for order from {$merchantName}";
    }

    private function generateRandomIP(): string
    {
        return rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
    }

    private function getPaymentGateway($paymentMethod): string
    {
        $gateways = [
            'bank_transfer' => 'Midtrans',
            'e_wallet' => 'GoPay',
            'credit_card' => 'Stripe',
            'qris' => 'DANA',
        ];

        return $gateways[$paymentMethod] ?? 'Unknown';
    }
}
