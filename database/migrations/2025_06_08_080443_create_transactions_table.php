<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
    $table->id();
    $table->foreignId('merchant_id')->constrained()->cascadeOnDelete();
    $table->string('reference_no')->unique();
    $table->string('merchant_reference')->nullable();
    $table->string('customer_name');
    $table->string('customer_email')->nullable();
    $table->string('customer_phone')->nullable();
    $table->decimal('amount', 12, 2);
    $table->decimal('fee', 12, 2)->default(0);
    $table->enum('status', ['pending', 'success', 'failed'])->default('pending');
    $table->timestamps();
});
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
