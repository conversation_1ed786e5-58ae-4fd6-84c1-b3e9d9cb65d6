<?php

namespace Database\Factories;

use App\Models\Merchant;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Merchant>
 */
class MerchantFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $merchantTypes = ['online_store', 'restaurant', 'service', 'retail', 'digital_service'];
        
        return [
            'user_id' => User::factory(),
            'merchant_code' => Merchant::generateMerchantCode(),
            'merchant_name' => fake()->company(),
            'merchant_type' => fake()->randomElement($merchantTypes),
            'description' => fake()->paragraph(2),
            'website_url' => fake()->optional(0.7)->url(),
            'contact_email' => fake()->optional(0.8)->safeEmail(),
            'contact_phone' => fake()->optional(0.9)->phoneNumber(),
            'address' => fake()->optional(0.6)->address(),
            'is_active' => fake()->boolean(90), // 90% chance of being active
        ];
    }

    /**
     * Indicate that the merchant is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the merchant is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a merchant for a specific user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }
}
