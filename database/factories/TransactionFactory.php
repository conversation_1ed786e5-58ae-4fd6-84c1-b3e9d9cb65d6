<?php

namespace Database\Factories;

use App\Models\Merchant;
use App\Models\Transaction;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Transaction>
 */
class TransactionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $amount = fake()->randomFloat(2, 10000, 5000000); // 10k - 5M IDR
        $feePercentage = fake()->randomFloat(4, 0.01, 0.03); // 1% - 3% fee
        $fee = $amount * $feePercentage;
        
        return [
            'merchant_id' => Merchant::factory(),
            'reference_no' => Transaction::generateReferenceNo(),
            'merchant_reference' => fake()->optional(0.7)->regexify('[A-Z0-9]{8,12}'),
            'customer_name' => fake()->name(),
            'customer_email' => fake()->optional(0.8)->safeEmail(),
            'customer_phone' => fake()->optional(0.9)->regexify('\+62[0-9]{9,12}'),
            'amount' => $amount,
            'fee' => $fee,
            'status' => fake()->randomElement(['pending', 'success', 'failed']),
        ];
    }

    /**
     * Indicate that the transaction is successful.
     */
    public function success(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'success',
        ]);
    }

    /**
     * Indicate that the transaction is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    /**
     * Indicate that the transaction is failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
        ]);
    }

    /**
     * Create a transaction for a specific merchant.
     */
    public function forMerchant(Merchant $merchant): static
    {
        return $this->state(fn (array $attributes) => [
            'merchant_id' => $merchant->id,
        ]);
    }

    /**
     * Create a transaction with a specific amount.
     */
    public function withAmount(float $amount): static
    {
        $feePercentage = fake()->randomFloat(4, 0.01, 0.03);
        $fee = $amount * $feePercentage;
        
        return $this->state(fn (array $attributes) => [
            'amount' => $amount,
            'fee' => $fee,
        ]);
    }
}
