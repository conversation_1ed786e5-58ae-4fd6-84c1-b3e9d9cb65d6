<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Hash;

// Create test users
$users = [
    [
        'name' => 'Admin User',
        'email' => '<EMAIL>',
        'password' => Hash::make('password'),
        'role' => 'admin',
        'phone' => '+6281234567890',
        'email_verified_at' => now(),
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'Regular User',
        'email' => '<EMAIL>',
        'password' => Hash::make('password'),
        'role' => 'user',
        'phone' => '+6281234567891',
        'email_verified_at' => now(),
        'created_at' => now(),
        'updated_at' => now(),
    ],
];

echo "Test users to create:\n";
foreach ($users as $user) {
    echo "- {$user['email']} (role: {$user['role']})\n";
}
echo "\nPassword for all users: password\n";
