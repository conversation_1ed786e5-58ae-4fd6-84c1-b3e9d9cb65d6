<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'merchant_id',
        'reference_no',
        'merchant_reference',
        'customer_name',
        'customer_email',
        'customer_phone',
        'amount',
        'fee',
        'status',
    ];

    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }
}
