<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Transaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'merchant_id',
        'reference_no',
        'merchant_reference',
        'customer_name',
        'customer_email',
        'customer_phone',
        'amount',
        'fee',
        'status',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'fee' => 'decimal:2',
    ];

    /**
     * Get the merchant that owns the transaction.
     */
    public function merchant(): BelongsTo
    {
        return $this->belongsTo(Merchant::class);
    }

    /**
     * Generate a unique reference number.
     */
    public static function generateReferenceNo(): string
    {
        do {
            $reference = 'TXN' . date('Ymd') . strtoupper(substr(md5(uniqid()), 0, 6));
        } while (self::where('reference_no', $reference)->exists());

        return $reference;
    }

    /**
     * Scope a query to only include successful transactions.
     */
    public function scopeSuccess($query)
    {
        return $query->where('status', 'success');
    }

    /**
     * Scope a query to only include pending transactions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include failed transactions.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Get the net amount (amount - fee).
     */
    public function getNetAmountAttribute()
    {
        return $this->amount - $this->fee;
    }

    /**
     * Get the status badge color.
     */
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'success' => 'green',
            'pending' => 'yellow',
            'failed' => 'red',
            default => 'gray'
        };
    }
}
