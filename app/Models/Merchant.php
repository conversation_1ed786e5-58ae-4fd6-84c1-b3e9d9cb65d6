<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Merchant extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'merchant_code',
        'merchant_name',
        'merchant_type',
        'description',
        'website_url',
        'contact_email',
        'contact_phone',
        'address',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns the merchant.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the transactions for the merchant.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Generate a unique merchant code.
     */
    public static function generateMerchantCode(): string
    {
        do {
            $code = 'MCH' . strtoupper(substr(md5(uniqid()), 0, 8));
        } while (self::where('merchant_code', $code)->exists());

        return $code;
    }

    /**
     * Scope a query to only include active merchants.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the total transaction amount for this merchant.
     */
    public function getTotalTransactionAmountAttribute()
    {
        return $this->transactions()->where('status', 'success')->sum('amount');
    }

    /**
     * Get the total transaction count for this merchant.
     */
    public function getTotalTransactionCountAttribute()
    {
        return $this->transactions()->count();
    }
}
