<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Models\Merchant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;
use Carbon\Carbon;

class TransactionController extends Controller
{
    /**
     * Display a listing of all transactions for the user's merchants.
     */
    public function index(Request $request): Response
    {
        $query = Transaction::query()
            ->whereHas('merchant', function ($query) {
                $query->where('user_id', Auth::id());
            })
            ->with(['merchant:id,merchant_name,merchant_code']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('merchant_id')) {
            $query->where('merchant_id', $request->merchant_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('reference_no', 'like', "%{$search}%")
                  ->orWhere('merchant_reference', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%");
            });
        }

        $transactions = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get user's merchants for filter dropdown
        $merchants = Auth::user()->merchants()
            ->select('id', 'merchant_name', 'merchant_code')
            ->orderBy('merchant_name')
            ->get();

        // Calculate summary stats
        $stats = [
            'total_transactions' => Auth::user()->transactions()->count(),
            'successful_transactions' => Auth::user()->transactions()->success()->count(),
            'pending_transactions' => Auth::user()->transactions()->pending()->count(),
            'failed_transactions' => Auth::user()->transactions()->failed()->count(),
            'total_amount' => Auth::user()->transactions()->success()->sum('amount'),
            'total_fee' => Auth::user()->transactions()->success()->sum('fee'),
        ];

        return Inertia::render('transaction/clean-index', [
            'transactions' => $transactions,
            'merchants' => $merchants,
            'filters' => $request->only(['status', 'merchant_id', 'date_from', 'date_to', 'search']),
            'stats' => $stats
        ]);
    }

    /**
     * Display the specified transaction.
     */
    public function show(Transaction $transaction): Response
    {
        // Ensure the transaction belongs to the authenticated user's merchant
        if ($transaction->merchant->user_id !== Auth::id()) {
            abort(403);
        }

        $transaction->load('merchant:id,merchant_name,merchant_code,user_id');

        return Inertia::render('transaction/show', [
            'transaction' => $transaction
        ]);
    }

    /**
     * Generate sample data for testing
     */
    public function generateSampleData(): RedirectResponse
    {
        $user = Auth::user();

        // Create sample merchants if user doesn't have any
        if ($user->merchants()->count() === 0) {
            $merchants = [
                [
                    'merchant_name' => 'Toko Online Sejahtera',
                    'merchant_type' => 'online_store',
                    'description' => 'Toko online yang menjual berbagai kebutuhan sehari-hari',
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '+*************',
                    'website_url' => 'https://tokosejahtera.com',
                    'address' => 'Jl. Sudirman No. 123, Jakarta Pusat',
                    'is_active' => true,
                ],
                [
                    'merchant_name' => 'Warung Kopi Nusantara',
                    'merchant_type' => 'restaurant',
                    'description' => 'Warung kopi dengan cita rasa khas Indonesia',
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '+*************',
                    'website_url' => 'https://kopinusantara.com',
                    'address' => 'Jl. Thamrin No. 456, Jakarta Pusat',
                    'is_active' => true,
                ],
                [
                    'merchant_name' => 'Fashion Store Modern',
                    'merchant_type' => 'retail',
                    'description' => 'Toko fashion dengan koleksi terkini',
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '+*************',
                    'website_url' => 'https://fashionmodern.com',
                    'address' => 'Jl. Gatot Subroto No. 789, Jakarta Selatan',
                    'is_active' => true,
                ],
            ];

            foreach ($merchants as $index => $merchantData) {
                Merchant::create(array_merge($merchantData, [
                    'user_id' => $user->id,
                    'merchant_code' => 'MCH' . str_pad($user->merchants()->count() + $index + 1, 3, '0', STR_PAD_LEFT),
                ]));
            }
        }

        // Get user's merchants
        $merchants = $user->merchants;

        // Create sample transactions
        $statuses = ['pending', 'success', 'failed'];
        $paymentMethods = ['bank_transfer', 'e_wallet', 'credit_card', 'qris'];

        $customers = [
            ['name' => 'Ahmad Rizki', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Siti Nurhaliza', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Budi Santoso', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Dewi Sartika', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Eko Prasetyo', 'email' => '<EMAIL>', 'phone' => '+*************'],
        ];

        for ($i = 1; $i <= 25; $i++) {
            $merchant = $merchants->random();
            $customer = $customers[array_rand($customers)];
            $status = $statuses[array_rand($statuses)];

            $amount = rand(10000, 1000000);
            $fee = $amount * 0.025;

            $createdAt = Carbon::now()->subDays(rand(0, 30));

            Transaction::create([
                'merchant_id' => $merchant->id,
                'reference_no' => Transaction::generateReferenceNo(),
                'merchant_reference' => 'ORD' . str_pad($i, 6, '0', STR_PAD_LEFT),
                'customer_name' => $customer['name'],
                'customer_email' => $customer['email'],
                'customer_phone' => $customer['phone'],
                'amount' => $amount,
                'fee' => $fee,
                'status' => $status,
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);
        }

        return redirect()->route('transaction.index')
            ->with('success', 'Sample data generated successfully! Created ' . $merchants->count() . ' merchants and 25 transactions.');
    }
}
