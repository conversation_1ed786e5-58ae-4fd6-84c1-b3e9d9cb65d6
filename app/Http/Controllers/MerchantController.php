<?php

namespace App\Http\Controllers;

use App\Models\Merchant;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class MerchantController extends Controller
{
    /**
     * Display a listing of the user's merchants.
     */
    public function index(): Response
    {
        $merchants = Auth::user()->merchants()
            ->withCount('transactions')
            ->with(['transactions' => function ($query) {
                $query->where('status', 'success');
            }])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Calculate total transaction amount for each merchant
        $merchants->getCollection()->transform(function ($merchant) {
            $merchant->total_transaction_amount = $merchant->transactions->sum('amount');
            return $merchant;
        });

        return Inertia::render('merchant/index', [
            'merchants' => $merchants
        ]);
    }

    /**
     * Show the form for creating a new merchant.
     */
    public function create(): Response
    {
        return Inertia::render('merchant/create');
    }

    /**
     * Store a newly created merchant in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'merchant_name' => ['required', 'string', 'max:255'],
            'merchant_type' => ['nullable', 'string', 'max:100'],
            'description' => ['nullable', 'string', 'max:1000'],
            'website_url' => ['nullable', 'url', 'max:255'],
            'contact_email' => ['nullable', 'email', 'max:255'],
            'contact_phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:500'],
        ]);

        $merchant = Auth::user()->merchants()->create([
            ...$validated,
            'merchant_code' => Merchant::generateMerchantCode(),
            'is_active' => true,
        ]);

        return redirect()->route('merchant.index')
            ->with('success', 'Merchant created successfully.');
    }

    /**
     * Display the specified merchant.
     */
    public function show(Merchant $merchant): Response
    {
        // Ensure the merchant belongs to the authenticated user
        if ($merchant->user_id !== Auth::id()) {
            abort(403);
        }

        $merchant->load(['transactions' => function ($query) {
            $query->orderBy('created_at', 'desc')->take(10);
        }]);

        $stats = [
            'total_transactions' => $merchant->transactions()->count(),
            'successful_transactions' => $merchant->transactions()->success()->count(),
            'pending_transactions' => $merchant->transactions()->pending()->count(),
            'failed_transactions' => $merchant->transactions()->failed()->count(),
            'total_amount' => $merchant->transactions()->success()->sum('amount'),
            'total_fee' => $merchant->transactions()->success()->sum('fee'),
        ];

        return Inertia::render('merchant/show', [
            'merchant' => $merchant,
            'stats' => $stats
        ]);
    }

    /**
     * Show the form for editing the specified merchant.
     */
    public function edit(Merchant $merchant): Response
    {
        // Ensure the merchant belongs to the authenticated user
        if ($merchant->user_id !== Auth::id()) {
            abort(403);
        }

        return Inertia::render('merchant/edit', [
            'merchant' => $merchant
        ]);
    }

    /**
     * Update the specified merchant in storage.
     */
    public function update(Request $request, Merchant $merchant): RedirectResponse
    {
        // Ensure the merchant belongs to the authenticated user
        if ($merchant->user_id !== Auth::id()) {
            abort(403);
        }

        $validated = $request->validate([
            'merchant_name' => ['required', 'string', 'max:255'],
            'merchant_type' => ['required', 'string', 'in:online_store,restaurant,retail,service,digital_service,other'],
            'description' => ['nullable', 'string', 'max:1000'],
            'website_url' => ['nullable', 'url', 'max:255'],
            'contact_email' => ['nullable', 'email', 'max:255'],
            'contact_phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:500'],
            'is_active' => ['boolean'],
        ]);

        $merchant->update($validated);

        return redirect()->route('merchant.show', $merchant)
            ->with('success', 'Merchant updated successfully.');
    }

    /**
     * Remove the specified merchant from storage.
     */
    public function destroy(Merchant $merchant): RedirectResponse
    {
        // Ensure the merchant belongs to the authenticated user
        if ($merchant->user_id !== Auth::id()) {
            abort(403);
        }

        // Check if merchant has transactions
        if ($merchant->transactions()->count() > 0) {
            return redirect()->route('merchant.index')
                ->with('error', 'Cannot delete merchant with existing transactions.');
        }

        $merchant->delete();

        return redirect()->route('merchant.index')
            ->with('success', 'Merchant deleted successfully.');
    }
}
