<?php

namespace App\Http\Controllers;

use App\Models\Merchant;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class MerchantController extends Controller
{
    /**
     * Display a listing of the user's merchants.
     */
    public function index(Request $request): Response
    {
        $query = Auth::user()->merchants()
            ->withCount('transactions')
            ->with(['transactions' => function ($query) {
                $query->where('status', 'success');
            }]);

        // Apply filters
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        if ($request->filled('merchant_type')) {
            $query->where('merchant_type', $request->merchant_type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('merchant_name', 'like', "%{$search}%")
                  ->orWhere('merchant_code', 'like', "%{$search}%")
                  ->orWhere('contact_email', 'like', "%{$search}%")
                  ->orWhere('contact_phone', 'like', "%{$search}%");
            });
        }

        // Get per_page from request, default to 25, max 100
        $perPage = $request->get('per_page', 25);
        $perPage = in_array($perPage, [10, 25, 50, 100]) ? $perPage : 25;

        $merchants = $query->orderBy('created_at', 'desc')->paginate($perPage);

        // Calculate total transaction amount for each merchant
        $merchants->getCollection()->transform(function ($merchant) {
            $merchant->total_transaction_amount = $merchant->transactions->sum('amount');
            $merchant->successful_transactions = $merchant->transactions()->success()->count();
            $merchant->total_revenue = $merchant->transactions()->success()->sum('amount') ?: 0;
            return $merchant;
        });

        // Calculate summary stats
        $stats = [
            'total_merchants' => Auth::user()->merchants()->count(),
            'active_merchants' => Auth::user()->merchants()->where('is_active', true)->count(),
            'inactive_merchants' => Auth::user()->merchants()->where('is_active', false)->count(),
            'total_revenue' => Auth::user()->merchants()->with('transactions')->get()->sum(function ($merchant) {
                return $merchant->transactions()->success()->sum('amount');
            }),
        ];

        return Inertia::render('merchant/clean', [
            'merchants' => $merchants,
            'filters' => $request->only(['status', 'merchant_type', 'search', 'per_page']),
            'stats' => $stats
        ]);
    }

    /**
     * Show the form for creating a new merchant.
     */
    public function create(): Response
    {
        return Inertia::render('merchant/create');
    }

    /**
     * Store a newly created merchant in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'merchant_name' => ['required', 'string', 'max:255'],
            'merchant_type' => ['nullable', 'string', 'max:100'],
            'description' => ['nullable', 'string', 'max:1000'],
            'website_url' => ['nullable', 'url', 'max:255'],
            'contact_email' => ['nullable', 'email', 'max:255'],
            'contact_phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:500'],
        ]);

        $merchant = Auth::user()->merchants()->create([
            ...$validated,
            'merchant_code' => Merchant::generateMerchantCode(),
            'is_active' => true,
        ]);

        return redirect()->route('merchant.index')
            ->with('success', 'Merchant created successfully.');
    }

    /**
     * Display the specified merchant.
     */
    public function show(Merchant $merchant): Response
    {
        // Ensure the merchant belongs to the authenticated user
        if ($merchant->user_id !== Auth::id()) {
            abort(403);
        }

        $merchant->load(['transactions' => function ($query) {
            $query->orderBy('created_at', 'desc')->take(10);
        }]);

        // Calculate merchant statistics
        $totalTransactions = $merchant->transactions()->count();
        $successfulTransactions = $merchant->transactions()->success()->count();
        $totalRevenue = $merchant->transactions()->success()->sum('amount') ?: 0;

        $stats = [
            'total_transactions' => $totalTransactions,
            'total_revenue' => $totalRevenue,
            'success_rate' => $totalTransactions > 0 ? ($successfulTransactions / $totalTransactions) * 100 : 0,
            'avg_transaction' => $successfulTransactions > 0 ? $totalRevenue / $successfulTransactions : 0,
        ];

        return Inertia::render('merchant/show', [
            'merchant' => $merchant,
            'stats' => $stats
        ]);
    }

    /**
     * Show the form for editing the specified merchant.
     */
    public function edit(Merchant $merchant): Response
    {
        // Ensure the merchant belongs to the authenticated user
        if ($merchant->user_id !== Auth::id()) {
            abort(403);
        }

        return Inertia::render('merchant/edit', [
            'merchant' => $merchant
        ]);
    }

    /**
     * Update the specified merchant in storage.
     */
    public function update(Request $request, Merchant $merchant): RedirectResponse
    {
        // Ensure the merchant belongs to the authenticated user
        if ($merchant->user_id !== Auth::id()) {
            abort(403);
        }

        $validated = $request->validate([
            'merchant_name' => ['required', 'string', 'max:255'],
            'merchant_type' => ['required', 'string', 'in:online_store,restaurant,retail,service,digital_service,other'],
            'description' => ['nullable', 'string', 'max:1000'],
            'website_url' => ['nullable', 'url', 'max:255'],
            'contact_email' => ['nullable', 'email', 'max:255'],
            'contact_phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:500'],
            'is_active' => ['boolean'],
        ]);

        $merchant->update($validated);

        return redirect()->route('merchant.show', $merchant)
            ->with('success', 'Merchant updated successfully.');
    }

    /**
     * Remove the specified merchant from storage.
     */
    public function destroy(Merchant $merchant): RedirectResponse
    {
        // Ensure the merchant belongs to the authenticated user
        if ($merchant->user_id !== Auth::id()) {
            abort(403);
        }

        // Check if merchant has transactions
        if ($merchant->transactions()->count() > 0) {
            return redirect()->route('merchant.index')
                ->with('error', 'Cannot delete merchant with existing transactions.');
        }

        $merchant->delete();

        return redirect()->route('merchant.index')
            ->with('success', 'Merchant deleted successfully.');
    }
}
