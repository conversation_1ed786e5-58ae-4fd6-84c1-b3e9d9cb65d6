<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class UserEditController extends Controller
{
    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user): Response
    {
        // Ensure only users with role 'user' can be edited
        if ($user->role !== 'user') {
            abort(404);
        }

        return Inertia::render('admin/edit-user', [
            'user' => $user->only([
                'id', 'name', 'email', 'phone', 'bank_name', 
                'account_number', 'account_holder_name', 'withdrawal_limit',
                'email_verified_at', 'created_at'
            ])
        ]);
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        // Ensure only users with role 'user' can be edited
        if ($user->role !== 'user') {
            abort(404);
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($user->id),
            ],
            'phone' => [
                'required',
                'string',
                'regex:/^\+62[0-9]{9,13}$/',
                Rule::unique(User::class)->ignore($user->id),
            ],
            // Bank Account Details
            'bank_name' => ['nullable', 'string', 'max:255'],
            'account_number' => ['nullable', 'string', 'max:50'],
            'account_holder_name' => ['nullable', 'string', 'max:255'],
            'withdrawal_limit' => ['nullable', 'numeric', 'min:0', 'max:************.99'],
        ]);

        $user->update($validated);

        return redirect()->route('admin.users.edit', $user)
            ->with('success', 'User updated successfully.');
    }
}
