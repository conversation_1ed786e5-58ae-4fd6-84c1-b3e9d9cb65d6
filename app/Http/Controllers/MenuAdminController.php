<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class MenuAdminController extends Controller
{
    /**
     * Display a listing of users with role 'user'.
     */
    public function index(): Response
    {
        $users = User::where('role', 'user')
            ->select(['id', 'name', 'email', 'phone', 'created_at', 'email_verified_at'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('menuadmin', [
            'users' => $users
        ]);
    }
}
