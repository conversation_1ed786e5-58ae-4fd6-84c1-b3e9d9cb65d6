<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Merchant;
use App\Models\Transaction;
use Carbon\Carbon;

class GenerateSampleData extends Command
{
    protected $signature = 'sample:generate';
    protected $description = 'Generate sample merchants and transactions data';

    public function handle()
    {
        $this->info('Generating sample data...');

        // Get or create a user
        $user = User::where('email', '<EMAIL>')->first();
        if (!$user) {
            $user = User::create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'user',
                'email_verified_at' => now(),
            ]);
            $this->info('Created test user: <EMAIL>');
        }

        // Create merchants if they don't exist
        if (Merchant::where('user_id', $user->id)->count() == 0) {
            $merchants = [
                [
                    'merchant_name' => 'Toko Online Sejahtera',
                    'merchant_type' => 'online_store',
                    'description' => 'Toko online yang menjual berb<PERSON>i kebutuhan se<PERSON>-hari',
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '+*************',
                    'website_url' => 'https://tokosejahtera.com',
                    'address' => 'Jl. Sudirman No. 123, Jakarta Pusat',
                    'is_active' => true,
                ],
                [
                    'merchant_name' => 'Warung Kopi Nusantara',
                    'merchant_type' => 'restaurant',
                    'description' => 'Warung kopi dengan cita rasa khas Indonesia',
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '+*************',
                    'website_url' => 'https://kopinusantara.com',
                    'address' => 'Jl. Thamrin No. 456, Jakarta Pusat',
                    'is_active' => true,
                ],
                [
                    'merchant_name' => 'Fashion Store Modern',
                    'merchant_type' => 'retail',
                    'description' => 'Toko fashion dengan koleksi terkini',
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '+*************',
                    'website_url' => 'https://fashionmodern.com',
                    'address' => 'Jl. Gatot Subroto No. 789, Jakarta Selatan',
                    'is_active' => true,
                ],
            ];

            foreach ($merchants as $index => $merchantData) {
                Merchant::create(array_merge($merchantData, [
                    'user_id' => $user->id,
                    'merchant_code' => 'MCH' . str_pad($index + 1, 3, '0', STR_PAD_LEFT),
                ]));
            }
            $this->info('Created 3 sample merchants');
        }

        // Get user's merchants
        $merchants = Merchant::where('user_id', $user->id)->get();

        // Clear existing transactions for this user
        Transaction::whereIn('merchant_id', $merchants->pluck('id'))->delete();

        // Create sample transactions
        $statuses = ['pending', 'success', 'failed'];
        $paymentMethods = ['bank_transfer', 'e_wallet', 'credit_card', 'qris'];
        
        $customers = [
            ['name' => 'Ahmad Rizki', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Siti Nurhaliza', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Budi Santoso', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Dewi Sartika', 'email' => '<EMAIL>', 'phone' => '+*************'],
            ['name' => 'Eko Prasetyo', 'email' => '<EMAIL>', 'phone' => '+*************'],
        ];

        for ($i = 1; $i <= 30; $i++) {
            $merchant = $merchants->random();
            $customer = $customers[array_rand($customers)];
            $status = $statuses[array_rand($statuses)];
            $paymentMethod = $paymentMethods[array_rand($paymentMethods)];
            
            $amount = rand(10000, 1000000);
            $fee = $amount * 0.025;
            
            $createdAt = Carbon::now()->subDays(rand(0, 30));
            
            Transaction::create([
                'merchant_id' => $merchant->id,
                'transaction_reference' => 'TXN' . str_pad($i, 6, '0', STR_PAD_LEFT),
                'amount' => $amount,
                'fee' => $fee,
                'status' => $status,
                'payment_method' => $paymentMethod,
                'customer_name' => $customer['name'],
                'customer_email' => $customer['email'],
                'customer_phone' => $customer['phone'],
                'description' => "Payment for order from {$merchant->merchant_name} via " . ucfirst(str_replace('_', ' ', $paymentMethod)),
                'metadata' => json_encode([
                    'ip_address' => rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255),
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                ]),
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);
        }

        $this->info('Created 30 sample transactions');
        $this->info('Sample data generation completed!');
        $this->info('You can login with: <EMAIL> / password');
    }
}
